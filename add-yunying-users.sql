-- ========================================
-- 运营部门用户添加脚本
-- 部门ID: 107 (运营部门)
-- 生成时间: 2025-01-04
-- ========================================

-- 插入运营部门用户数据
-- 默认密码为: admin123 (加密后的密码)
-- 状态: 0 (正常)
-- 租户ID: 1 (默认租户)

INSERT INTO system_users (
    username, 
    password, 
    nickname, 
    remark, 
    dept_id, 
    post_ids, 
    email, 
    mobile, 
    sex, 
    avatar, 
    status, 
    create_time, 
    update_time, 
    creator, 
    updater, 
    deleted, 
    tenant_id
) VALUES 
-- 1. 丁凯
(
    'dingkai',
    '{bcrypt}$2a$10$mRMIYLDtRHlf6.9ipiqH1.Z.bh/R9dO9d5iHiGYPigi6r5KOoR2Wm',
    '丁凯',
    '运营部门员工',
    107,
    '[]',
    '<EMAIL>',
    '',
    0,
    '',
    0,
    NOW(),
    NOW(),
    'system',
    'system',
    0,
    1
),

-- 2. 李明磊
(
    'liminglei',
    '{bcrypt}$2a$10$mRMIYLDtRHlf6.9ipiqH1.Z.bh/R9dO9d5iHiGYPigi6r5KOoR2Wm',
    '李明磊',
    '运营部门员工',
    107,
    '[]',
    '<EMAIL>',
    '',
    0,
    '',
    0,
    NOW(),
    NOW(),
    'system',
    'system',
    0,
    1
),

-- 3. 张光烁
(
    'zhangguangshuo',
    '{bcrypt}$2a$10$mRMIYLDtRHlf6.9ipiqH1.Z.bh/R9dO9d5iHiGYPigi6r5KOoR2Wm',
    '张光烁',
    '运营部门员工',
    107,
    '[]',
    '<EMAIL>',
    '',
    0,
    '',
    0,
    NOW(),
    NOW(),
    'system',
    'system',
    0,
    1
),

-- 4. 郭梦含
(
    'guomenghan',
    '{bcrypt}$2a$10$mRMIYLDtRHlf6.9ipiqH1.Z.bh/R9dO9d5iHiGYPigi6r5KOoR2Wm',
    '郭梦含',
    '运营部门员工',
    107,
    '[]',
    '<EMAIL>',
    '15736843090',
    0,
    '',
    0,
    NOW(),
    NOW(),
    'system',
    'system',
    0,
    1
),

-- 5. 孟雪倩
(
    'mengxueqian',
    '{bcrypt}$2a$10$mRMIYLDtRHlf6.9ipiqH1.Z.bh/R9dO9d5iHiGYPigi6r5KOoR2Wm',
    '孟雪倩',
    '运营部门员工',
    107,
    '[]',
    '<EMAIL>',
    '',
    0,
    '',
    0,
    NOW(),
    NOW(),
    'system',
    'system',
    0,
    1
),

-- 6. 曹嘉处
(
    'caojiachu',
    '{bcrypt}$2a$10$mRMIYLDtRHlf6.9ipiqH1.Z.bh/R9dO9d5iHiGYPigi6r5KOoR2Wm',
    '曹嘉处',
    '运营部门员工',
    107,
    '[]',
    '<EMAIL>',
    '13333998123',
    0,
    '',
    0,
    NOW(),
    NOW(),
    'system',
    'system',
    0,
    1
);

-- ========================================
-- 执行说明:
-- 1. 所有用户的默认密码为: admin123
-- 2. 用户状态为正常 (status = 0)
-- 3. 部门ID为107 (运营部门)
-- 4. 从邮箱中提取了手机号码 (郭梦含、曹嘉处)
-- 5. 用户名使用姓名拼音，昵称使用真实姓名
-- ========================================

-- 验证插入结果的查询语句
-- SELECT id, username, nickname, email, mobile, dept_id, status 
-- FROM system_users 
-- WHERE dept_id = 107 
-- ORDER BY create_time DESC;
