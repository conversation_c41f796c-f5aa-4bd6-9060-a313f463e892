# 前端页面功能规划

## 1. 页面结构设计

### 1.1 菜单结构
```
资质管理
├── 资质汇总 (现有)
├── VC账号管理 (新增)
│   ├── VC账号列表
│   ├── 负责人分配
│   └── VC账号统计
└── 资质统计 (现有，增强)
```

### 1.2 页面路由
```typescript
// 路由配置
const routes = [
  {
    path: '/qualification',
    name: 'Qualification',
    children: [
      {
        path: 'summary',
        name: 'QualificationSummary',
        component: () => import('@/views/qualification/summary/index.vue')
      },
      {
        path: 'vc-account',
        name: 'VcAccount',
        component: () => import('@/views/qualification/vc-account/index.vue')
      },
      {
        path: 'vc-account/manager',
        name: 'VcAccountManager',
        component: () => import('@/views/qualification/vc-account/manager.vue')
      }
    ]
  }
]
```

## 2. VC账号管理页面

### 2.1 VC账号列表页面 (`/qualification/vc-account/index.vue`)

**功能特性：**
- 表格展示VC账号信息
- 支持多条件筛选（账号编码、名称、负责人、状态等）
- 支持新增、编辑、删除VC账号
- 支持批量操作
- 支持导入导出
- 显示关联资质数量

**表格列设计：**
```typescript
const columns = [
  { field: 'accountCode', title: 'VC账号编码', width: 150 },
  { field: 'accountName', title: 'VC账号名称', width: 200 },
  { field: 'accountType', title: '账号类型', width: 120 },
  { field: 'companyName', title: '公司名称', width: 200 },
  { field: 'managerUserName', title: '负责人', width: 120 },
  { field: 'qualificationCount', title: '资质数量', width: 100 },
  { field: 'expiringCount', title: '即将过期', width: 100 },
  { field: 'status', title: '状态', width: 80 },
  { field: 'createTime', title: '创建时间', width: 150 },
  { field: 'actions', title: '操作', width: 200, fixed: 'right' }
]
```

**筛选条件：**
```typescript
const searchForm = {
  accountCode: '', // 账号编码
  accountName: '', // 账号名称
  managerUserId: null, // 负责人
  status: null, // 状态
  accountType: '', // 账号类型
  createTime: [] // 创建时间范围
}
```

### 2.2 VC账号新增/编辑弹窗

**表单字段：**
```typescript
const formSchema = [
  {
    field: 'accountCode',
    label: 'VC账号编码',
    component: 'Input',
    required: true,
    rules: [
      { required: true, message: '请输入VC账号编码' },
      { pattern: /^[A-Z0-9_]{3,20}$/, message: '编码格式不正确' }
    ]
  },
  {
    field: 'accountName',
    label: 'VC账号名称',
    component: 'Input',
    required: true
  },
  {
    field: 'accountType',
    label: '账号类型',
    component: 'Select',
    componentProps: {
      options: [
        { label: '旗舰店', value: '旗舰店' },
        { label: '专营店', value: '专营店' },
        { label: '代理商', value: '代理商' }
      ]
    }
  },
  {
    field: 'managerUserId',
    label: '负责人',
    component: 'ApiSelect',
    componentProps: {
      api: getUserSimpleList,
      labelField: 'nickname',
      valueField: 'id'
    },
    required: true
  },
  {
    field: 'priorityLevel',
    label: '优先级',
    component: 'RadioGroup',
    componentProps: {
      options: [
        { label: '高', value: 1 },
        { label: '中', value: 2 },
        { label: '低', value: 3 }
      ]
    }
  }
]
```

## 3. 负责人分配页面

### 3.1 负责人分配弹窗

**功能特性：**
- 支持为VC账号分配多个负责人
- 区分主负责人、副负责人、协助负责人
- 支持从用户列表中选择
- 支持批量分配和移除

**组件设计：**
```vue
<template>
  <div class="manager-assignment">
    <div class="current-managers">
      <h4>当前负责人</h4>
      <el-table :data="currentManagers">
        <el-table-column prop="userName" label="姓名" />
        <el-table-column prop="managerType" label="类型">
          <template #default="{ row }">
            <el-tag :type="getManagerTypeTag(row.managerType)">
              {{ getManagerTypeName(row.managerType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template #default="{ row }">
            <el-button type="danger" size="small" @click="removeManager(row)">
              移除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    
    <div class="add-manager">
      <h4>添加负责人</h4>
      <el-form :model="addForm">
        <el-form-item label="选择用户">
          <el-select v-model="addForm.userId" filterable>
            <el-option
              v-for="user in availableUsers"
              :key="user.id"
              :label="user.nickname"
              :value="user.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="负责人类型">
          <el-radio-group v-model="addForm.managerType">
            <el-radio :label="1">主负责人</el-radio>
            <el-radio :label="2">副负责人</el-radio>
            <el-radio :label="3">协助负责人</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="addManager">添加</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>
```

## 4. 资质管理页面增强

### 4.1 资质列表页面增强

**新增功能：**
- VC账号筛选下拉框
- 按负责人筛选（我负责的VC账号）
- VC账号信息展示
- 快速切换VC账号视图

**筛选条件增强：**
```typescript
const enhancedSearchForm = {
  // 原有字段...
  vcAccountId: null, // VC账号筛选
  myManagedOnly: false, // 只看我负责的
  managerUserId: null, // 负责人筛选
}
```

### 4.2 VC账号选择组件

```vue
<template>
  <div class="vc-account-selector">
    <el-select
      v-model="selectedVcAccount"
      placeholder="选择VC账号"
      filterable
      clearable
      @change="handleVcAccountChange"
    >
      <el-option-group label="我负责的VC账号">
        <el-option
          v-for="account in myManagedAccounts"
          :key="account.id"
          :label="account.accountName"
          :value="account.id"
        >
          <span>{{ account.accountName }}</span>
          <span class="option-desc">{{ account.accountCode }}</span>
        </el-option>
      </el-option-group>
      
      <el-option-group label="所有VC账号" v-if="hasAllPermission">
        <el-option
          v-for="account in allAccounts"
          :key="account.id"
          :label="account.accountName"
          :value="account.id"
        >
          <span>{{ account.accountName }}</span>
          <span class="option-desc">{{ account.accountCode }}</span>
        </el-option>
      </el-option-group>
    </el-select>
  </div>
</template>
```

## 5. 统计分析页面

### 5.1 VC账号统计仪表板

**统计指标：**
- VC账号总数
- 活跃VC账号数
- 资质总数
- 即将过期资质数
- 负责人工作量分布

**图表组件：**
```vue
<template>
  <div class="vc-statistics">
    <el-row :gutter="20">
      <el-col :span="6">
        <el-card>
          <div class="stat-item">
            <div class="stat-value">{{ statistics.totalAccounts }}</div>
            <div class="stat-label">VC账号总数</div>
          </div>
        </el-card>
      </el-col>
      <!-- 其他统计卡片... -->
    </el-row>
    
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="12">
        <el-card title="负责人工作量分布">
          <div ref="workloadChart" style="height: 300px;"></div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card title="资质到期趋势">
          <div ref="expiringChart" style="height: 300px;"></div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>
```

## 6. 权限控制实现

### 6.1 路由权限守卫

```typescript
// 权限检查
const hasPermission = (permission: string) => {
  return userStore.permissions.includes(permission)
}

// 数据权限过滤
const filterDataByPermission = (data: any[]) => {
  if (hasPermission('vc:account:query-all')) {
    return data // 可以查看所有数据
  } else if (hasPermission('vc:account:manage-own')) {
    return data.filter(item => item.managerUserId === userStore.userId)
  }
  return [] // 无权限
}
```

### 6.2 按钮权限控制

```vue
<template>
  <el-button
    v-if="hasPermission('vc:account:create')"
    type="primary"
    @click="handleCreate"
  >
    新增VC账号
  </el-button>
  
  <el-button
    v-if="hasPermission('vc:account:assign-manager')"
    type="success"
    @click="handleAssignManager"
  >
    分配负责人
  </el-button>
</template>
```
