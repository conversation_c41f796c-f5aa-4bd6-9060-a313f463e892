# VC账号管理系统实现路线图

## 🎯 **总体实施策略**

**实施原则：**
- 渐进式开发，确保系统稳定性
- 先核心功能，后扩展功能
- 数据安全优先，做好备份和回滚方案
- 充分测试，确保与现有系统兼容

## 📅 **分阶段实施计划**

### **第一阶段：基础架构搭建（优先级：🔴 高）**
**预计工期：5-7个工作日**

#### 1.1 数据库设计与实施
- [ ] 创建VC账号管理表结构
- [ ] 创建数据迁移脚本
- [ ] 执行数据库变更（需要在维护窗口进行）
- [ ] 验证数据完整性

**关键文件：**
```
sql/
├── vc_account_tables.sql          # 表结构创建
├── data_migration.sql             # 数据迁移
└── rollback_scripts.sql           # 回滚脚本
```

#### 1.2 后端基础代码生成
- [ ] 使用代码生成器生成基础CRUD代码
- [ ] 创建VC账号相关的DO、VO、Service、Controller
- [ ] 配置MyBatis映射文件

**生成命令：**
```bash
# 使用yudao代码生成器
mvn exec:java -Dexec.mainClass="cn.iocoder.yudao.module.infra.service.codegen.CodegenEngine" \
  -Dexec.args="vc_account"
```

### **第二阶段：核心功能开发（优先级：🔴 高）**
**预计工期：8-10个工作日**

#### 2.1 后端核心功能实现
- [ ] VC账号管理Service实现
- [ ] 负责人分配功能
- [ ] 权限控制逻辑
- [ ] 数据校验和业务规则

**开发顺序：**
1. `VcAccountDO` - 实体类
2. `VcAccountMapper` - 数据访问层
3. `VcAccountService` - 业务逻辑层
4. `VcAccountController` - 控制器层
5. `VcAccountManagerService` - 负责人管理服务

#### 2.2 API接口开发
- [ ] VC账号CRUD接口
- [ ] 负责人分配接口
- [ ] 权限验证接口
- [ ] 数据统计接口

**接口清单：**
```java
// VcAccountController.java
@PostMapping("/create")           // 创建VC账号
@PutMapping("/update")            // 更新VC账号
@DeleteMapping("/delete")         // 删除VC账号
@GetMapping("/get")               // 获取详情
@GetMapping("/page")              // 分页查询
@GetMapping("/simple-list")       // 简单列表
@PostMapping("/assign-manager")   // 分配负责人
```

### **第三阶段：前端界面开发（优先级：🟡 中）**
**预计工期：6-8个工作日**

#### 3.1 页面组件开发
- [ ] VC账号列表页面
- [ ] VC账号新增/编辑弹窗
- [ ] 负责人分配组件
- [ ] VC账号选择器组件

**文件结构：**
```
src/views/qualification/vc-account/
├── index.vue                     # VC账号列表页
├── components/
│   ├── VcAccountForm.vue         # 新增/编辑表单
│   ├── ManagerAssignment.vue     # 负责人分配
│   └── VcAccountSelector.vue     # VC账号选择器
├── data.ts                       # 表格配置和表单schema
└── api.ts                        # API接口调用
```

#### 3.2 现有页面增强
- [ ] 资质管理页面增加VC账号筛选
- [ ] 权限控制集成
- [ ] 用户体验优化

### **第四阶段：系统集成与测试（优先级：🟡 中）**
**预计工期：4-5个工作日**

#### 4.1 系统集成
- [ ] 与现有资质管理系统集成
- [ ] 权限系统集成
- [ ] 菜单和路由配置

#### 4.2 测试验证
- [ ] 单元测试编写
- [ ] 集成测试
- [ ] 用户验收测试
- [ ] 性能测试

**测试用例覆盖：**
- VC账号CRUD操作
- 负责人分配和权限验证
- 数据迁移验证
- 并发操作测试

### **第五阶段：高级功能开发（优先级：🟢 低）**
**预计工期：5-7个工作日**

#### 5.1 统计分析功能
- [ ] VC账号统计仪表板
- [ ] 负责人工作量分析
- [ ] 资质到期预警

#### 5.2 扩展功能
- [ ] 批量操作功能
- [ ] 导入导出功能
- [ ] 操作日志记录
- [ ] 消息通知功能

## 🛠️ **技术实现细节**

### **代码生成配置**
```yaml
# codegen配置
table:
  name: vc_account
  comment: VC账号管理
module: qualification
business: vcaccount
class: VcAccount
author: yudao
```

### **关键代码示例**

#### VcAccountDO实体类
```java
@TableName("vc_account")
@Data
@EqualsAndHashCode(callSuper = true)
public class VcAccountDO extends TenantBaseDO {
    @TableId
    private Long id;
    private String accountCode;
    private String accountName;
    private String accountType;
    private Long managerUserId;
    private String managerUserName;
    private Integer status;
    // ... 其他字段
}
```

#### 权限控制Service
```java
@Service
public class VcAccountPermissionService {
    
    public List<VcAccountDO> filterByPermission(List<VcAccountDO> accounts, Long userId) {
        // 根据用户权限过滤数据
        if (hasPermission(userId, "vc:account:query-all")) {
            return accounts;
        } else if (hasPermission(userId, "vc:account:manage-own")) {
            return accounts.stream()
                .filter(account -> Objects.equals(account.getManagerUserId(), userId))
                .collect(Collectors.toList());
        }
        return Collections.emptyList();
    }
}
```

## 🚨 **风险控制与应急预案**

### **数据安全措施**
1. **数据备份**：实施前完整备份现有数据
2. **分步迁移**：先在测试环境验证，再生产环境执行
3. **回滚方案**：准备完整的数据回滚脚本
4. **监控告警**：设置数据一致性监控

### **系统稳定性保障**
1. **灰度发布**：先部分用户试用，再全量发布
2. **性能监控**：监控新功能对系统性能的影响
3. **错误处理**：完善的异常处理和用户提示
4. **日志记录**：详细的操作日志便于问题排查

### **用户培训计划**
1. **管理员培训**：VC账号管理和负责人分配
2. **普通用户培训**：新界面使用和权限变化
3. **操作手册**：详细的功能使用说明
4. **FAQ文档**：常见问题解答

## 📊 **项目里程碑**

| 里程碑 | 完成标准 | 预计时间 |
|--------|----------|----------|
| M1: 数据库就绪 | 表结构创建完成，数据迁移验证通过 | 第1周 |
| M2: 后端API完成 | 所有核心接口开发完成，单元测试通过 | 第2-3周 |
| M3: 前端界面完成 | 主要页面开发完成，基本功能可用 | 第4-5周 |
| M4: 系统集成完成 | 与现有系统集成，权限控制生效 | 第6周 |
| M5: 测试验收通过 | 所有测试用例通过，用户验收完成 | 第7周 |
| M6: 生产发布 | 系统正式上线，监控正常 | 第8周 |

## 🎯 **成功标准**

### **功能完整性**
- [ ] VC账号可以正常创建、编辑、删除
- [ ] 负责人分配功能正常工作
- [ ] 权限控制按预期生效
- [ ] 资质与VC账号正确关联

### **性能指标**
- [ ] 页面加载时间 < 2秒
- [ ] API响应时间 < 500ms
- [ ] 支持并发用户数 > 100

### **用户体验**
- [ ] 界面友好，操作直观
- [ ] 错误提示清晰明确
- [ ] 数据展示准确完整
- [ ] 权限控制透明合理
