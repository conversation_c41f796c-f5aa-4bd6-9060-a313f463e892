-- 测试用户角色分配功能的SQL脚本
-- 用于验证修复后的功能是否正常工作

-- 1. 查看当前用户147的角色分配情况
SELECT 
    u.id as user_id,
    u.username,
    u.nickname,
    ur.role_id,
    r.name as role_name
FROM system_users u
LEFT JOIN system_user_role ur ON u.id = ur.user_id
LEFT JOIN system_roles r ON ur.role_id = r.id
WHERE u.id = 147;

-- 2. 查看角色159的详细信息
SELECT 
    id,
    name,
    code,
    status,
    type
FROM system_roles 
WHERE id = 159;

-- 3. 检查是否存在用户147和角色159的关联记录
SELECT COUNT(*) as count
FROM system_user_role 
WHERE user_id = 147 AND role_id = 159;

-- 4. 如果需要手动插入测试数据（仅在测试环境使用）
-- INSERT INTO system_user_role (user_id, role_id, creator, create_time, updater, update_time, deleted, tenant_id)
-- VALUES (147, 159, 'system', NOW(), 'system', NOW(), 0, 1);

-- 5. 验证插入结果
-- SELECT * FROM system_user_role WHERE user_id = 147 AND role_id = 159;
