# VC账号管理API接口设计

## 1. VC账号管理接口

### 1.1 创建VC账号
```http
POST /admin-api/vc/account/create
Content-Type: application/json

{
  "accountCode": "TEST001",
  "accountName": "测试VC账号",
  "accountType": "旗舰店",
  "companyName": "测试公司有限公司",
  "contactPerson": "张三",
  "contactPhone": "***********",
  "contactEmail": "<EMAIL>",
  "businessScope": "电子产品销售",
  "regionCode": "310000",
  "regionName": "上海市",
  "managerUserId": 1,
  "backupManagerUserId": 2,
  "priorityLevel": 1,
  "status": 1,
  "remark": "测试账号"
}
```

### 1.2 更新VC账号
```http
PUT /admin-api/vc/account/update
Content-Type: application/json

{
  "id": 1,
  "accountName": "更新后的VC账号名称",
  "managerUserId": 3,
  "status": 1
}
```

### 1.3 删除VC账号
```http
DELETE /admin-api/vc/account/delete?id=1
```

### 1.4 获取VC账号详情
```http
GET /admin-api/vc/account/get?id=1
```

### 1.5 VC账号分页查询
```http
GET /admin-api/vc/account/page?pageNo=1&pageSize=10&accountCode=TEST&managerUserId=1&status=1
```

### 1.6 获取VC账号简单列表（用于下拉选择）
```http
GET /admin-api/vc/account/simple-list
```

## 2. VC账号负责人管理接口

### 2.1 分配负责人
```http
POST /admin-api/vc/account/assign-manager
Content-Type: application/json

{
  "vcAccountId": 1,
  "managerList": [
    {
      "userId": 1,
      "managerType": 1
    },
    {
      "userId": 2,
      "managerType": 2
    }
  ]
}
```

### 2.2 获取VC账号负责人列表
```http
GET /admin-api/vc/account/manager/list?vcAccountId=1
```

### 2.3 移除负责人
```http
DELETE /admin-api/vc/account/manager/remove?vcAccountId=1&userId=2
```

## 3. 资质管理接口增强

### 3.1 按VC账号查询资质（增强现有接口）
```http
GET /admin-api/qualification/summary/page?vcAccountId=1&pageNo=1&pageSize=10
```

### 3.2 获取用户负责的VC账号列表
```http
GET /admin-api/vc/account/my-managed-accounts
```

### 3.3 批量转移资质到其他VC账号
```http
POST /admin-api/qualification/summary/transfer-vc-account
Content-Type: application/json

{
  "qualificationIds": [1, 2, 3],
  "targetVcAccountId": 2,
  "reason": "账号合并"
}
```

## 4. 统计分析接口

### 4.1 VC账号资质统计
```http
GET /admin-api/vc/account/statistics/qualification-count?vcAccountId=1
```

### 4.2 负责人工作量统计
```http
GET /admin-api/vc/account/statistics/manager-workload?userId=1
```

### 4.3 VC账号资质到期预警
```http
GET /admin-api/vc/account/statistics/expiring-qualifications?days=30
```

## 5. 权限控制说明

### 5.1 权限码定义
- `vc:account:query` - 查看VC账号
- `vc:account:create` - 创建VC账号
- `vc:account:update` - 修改VC账号
- `vc:account:delete` - 删除VC账号
- `vc:account:assign-manager` - 分配负责人
- `vc:account:manage-own` - 管理自己负责的VC账号
- `qualification:summary:query-by-vc` - 按VC账号查询资质

### 5.2 数据权限控制
- 超级管理员：可以查看和管理所有VC账号
- 部门管理员：可以查看和管理本部门用户负责的VC账号
- 普通用户：只能查看和管理自己负责的VC账号

## 6. 响应格式示例

### 6.1 成功响应
```json
{
  "code": 0,
  "data": {
    "id": 1,
    "accountCode": "TEST001",
    "accountName": "测试VC账号",
    "managerUserName": "张三",
    "qualificationCount": 15,
    "expiringCount": 3
  },
  "msg": "操作成功"
}
```

### 6.2 分页响应
```json
{
  "code": 0,
  "data": {
    "list": [...],
    "total": 100,
    "pageNo": 1,
    "pageSize": 10
  },
  "msg": "查询成功"
}
```

### 6.3 错误响应
```json
{
  "code": 400,
  "data": null,
  "msg": "VC账号编码已存在"
}
```
