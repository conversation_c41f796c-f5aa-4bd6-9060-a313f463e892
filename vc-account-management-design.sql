-- ========================================
-- VC账号管理表结构设计
-- ========================================

-- 1. VC账号管理主表
CREATE TABLE `vc_account` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `account_code` varchar(100) NOT NULL COMMENT 'VC账号编码（唯一标识）',
  `account_name` varchar(200) NOT NULL COMMENT 'VC账号名称',
  `account_type` varchar(50) DEFAULT NULL COMMENT '账号类型（如：旗舰店、专营店、代理商等）',
  `company_name` varchar(300) DEFAULT NULL COMMENT '公司名称',
  `contact_person` varchar(100) DEFAULT NULL COMMENT '联系人',
  `contact_phone` varchar(50) DEFAULT NULL COMMENT '联系电话',
  `contact_email` varchar(100) DEFAULT NULL COMMENT '联系邮箱',
  `business_scope` text COMMENT '经营范围',
  `region_code` varchar(50) DEFAULT NULL COMMENT '所属区域编码',
  `region_name` varchar(100) DEFAULT NULL COMMENT '所属区域名称',
  `manager_user_id` bigint(20) DEFAULT NULL COMMENT '负责人用户ID（关联system_users表）',
  `manager_user_name` varchar(100) DEFAULT NULL COMMENT '负责人姓名（冗余字段）',
  `backup_manager_user_id` bigint(20) DEFAULT NULL COMMENT '备用负责人用户ID',
  `backup_manager_user_name` varchar(100) DEFAULT NULL COMMENT '备用负责人姓名',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态（1正常 0停用）',
  `priority_level` tinyint(4) DEFAULT '3' COMMENT '优先级（1高 2中 3低）',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_account_code_tenant` (`account_code`, `tenant_id`, `deleted`),
  KEY `idx_manager_user_id` (`manager_user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='VC账号管理表';

-- 2. VC账号负责人关联表（支持多负责人）
CREATE TABLE `vc_account_manager` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `vc_account_id` bigint(20) NOT NULL COMMENT 'VC账号ID',
  `user_id` bigint(20) NOT NULL COMMENT '负责人用户ID',
  `user_name` varchar(100) DEFAULT NULL COMMENT '负责人姓名（冗余）',
  `manager_type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '负责人类型（1主负责人 2副负责人 3协助负责人）',
  `assign_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '分配时间',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态（1正常 0停用）',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_vc_user_type` (`vc_account_id`, `user_id`, `manager_type`, `deleted`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='VC账号负责人关联表';

-- 3. 修改现有资质表，增加VC账号ID关联
ALTER TABLE `qualification_summary` 
ADD COLUMN `vc_account_id` bigint(20) DEFAULT NULL COMMENT 'VC账号ID（关联vc_account表）' AFTER `vc_account`,
ADD INDEX `idx_vc_account_id` (`vc_account_id`);

-- 4. 数据迁移脚本（将现有vc_account字段数据迁移到新表）
-- 注意：这个脚本需要根据实际数据情况调整
INSERT INTO `vc_account` (
    `account_code`, 
    `account_name`, 
    `status`, 
    `creator`, 
    `create_time`, 
    `updater`, 
    `update_time`, 
    `tenant_id`
)
SELECT DISTINCT 
    `vc_account` as `account_code`,
    `vc_account` as `account_name`,
    1 as `status`,
    'system' as `creator`,
    NOW() as `create_time`,
    'system' as `updater`,
    NOW() as `update_time`,
    `tenant_id`
FROM `qualification_summary` 
WHERE `vc_account` IS NOT NULL 
  AND `vc_account` != '' 
  AND `deleted` = 0;

-- 5. 更新资质表的vc_account_id字段
UPDATE `qualification_summary` qs
INNER JOIN `vc_account` va ON qs.`vc_account` = va.`account_code` 
   AND qs.`tenant_id` = va.`tenant_id`
SET qs.`vc_account_id` = va.`id`
WHERE qs.`deleted` = 0 AND va.`deleted` = 0;

-- 6. 创建视图，便于查询带负责人信息的VC账号
CREATE VIEW `v_vc_account_with_manager` AS
SELECT 
    va.`id`,
    va.`account_code`,
    va.`account_name`,
    va.`account_type`,
    va.`company_name`,
    va.`manager_user_id`,
    va.`manager_user_name`,
    u.`username` as `manager_username`,
    u.`nickname` as `manager_nickname`,
    u.`email` as `manager_email`,
    u.`mobile` as `manager_mobile`,
    d.`name` as `manager_dept_name`,
    va.`status`,
    va.`priority_level`,
    va.`create_time`,
    va.`update_time`,
    va.`tenant_id`
FROM `vc_account` va
LEFT JOIN `system_users` u ON va.`manager_user_id` = u.`id` AND u.`deleted` = 0
LEFT JOIN `system_dept` d ON u.`dept_id` = d.`id` AND d.`deleted` = 0
WHERE va.`deleted` = 0;

-- 7. 初始化权限数据
INSERT INTO `system_menu` (
    `name`, `permission`, `type`, `sort`, `parent_id`, `path`, `icon`, `component`, `component_name`, 
    `status`, `visible`, `keep_alive`, `always_show`, `creator`, `create_time`, `updater`, `update_time`, `deleted`
) VALUES 
('VC账号管理', 'vc:account:query', 2, 1, (SELECT id FROM system_menu WHERE permission = 'qualification:summary:query' LIMIT 1), 'vc-account', 'ep:user-filled', 'vc/account/index', 'VcAccount', 0, 1, 1, 1, 'system', NOW(), 'system', NOW(), 0),
('VC账号新增', 'vc:account:create', 3, 1, LAST_INSERT_ID(), '', '', '', '', 0, 1, 1, 1, 'system', NOW(), 'system', NOW(), 0),
('VC账号修改', 'vc:account:update', 3, 2, LAST_INSERT_ID()-1, '', '', '', '', 0, 1, 1, 1, 'system', NOW(), 'system', NOW(), 0),
('VC账号删除', 'vc:account:delete', 3, 3, LAST_INSERT_ID()-2, '', '', '', '', 0, 1, 1, 1, 'system', NOW(), 'system', NOW(), 0),
('负责人分配', 'vc:account:assign-manager', 3, 4, LAST_INSERT_ID()-3, '', '', '', '', 0, 1, 1, 1, 'system', NOW(), 'system', NOW(), 0);
