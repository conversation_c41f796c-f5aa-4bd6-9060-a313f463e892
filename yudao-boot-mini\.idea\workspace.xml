<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="b4656c92-87c5-4a1d-ad6c-49f0cd918aa8" name="更改" comment="">
      <change beforePath="$PROJECT_DIR$/../.claude/settings.local.json" beforeDir="false" afterPath="$PROJECT_DIR$/../.claude/settings.local.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../shrimp-rules.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/script/docker/docker-compose.yml" beforeDir="false" afterPath="$PROJECT_DIR$/script/docker/docker-compose.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/script/docker/docker.env" beforeDir="false" afterPath="$PROJECT_DIR$/script/docker/docker.env" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/sql/mysql/ruoyi-vue-pro.sql" beforeDir="false" afterPath="$PROJECT_DIR$/sql/mysql/ruoyi-vue-pro.sql" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/biz/infra/logger/ApiAccessLogCommonApi.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/biz/infra/logger/ApiAccessLogCommonApi.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/biz/infra/logger/ApiErrorLogCommonApi.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/biz/infra/logger/ApiErrorLogCommonApi.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/biz/infra/logger/dto/ApiAccessLogCreateReqDTO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/biz/infra/logger/dto/ApiAccessLogCreateReqDTO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/biz/infra/logger/dto/ApiErrorLogCreateReqDTO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/biz/infra/logger/dto/ApiErrorLogCreateReqDTO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/biz/system/dict/DictDataCommonApi.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/biz/system/dict/DictDataCommonApi.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/biz/system/dict/dto/DictDataRespDTO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/biz/system/dict/dto/DictDataRespDTO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/biz/system/logger/OperateLogCommonApi.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/biz/system/logger/OperateLogCommonApi.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/biz/system/logger/dto/OperateLogCreateReqDTO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/biz/system/logger/dto/OperateLogCreateReqDTO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/biz/system/oauth2/OAuth2TokenCommonApi.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/biz/system/oauth2/OAuth2TokenCommonApi.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/biz/system/oauth2/dto/OAuth2AccessTokenCheckRespDTO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/biz/system/oauth2/dto/OAuth2AccessTokenCheckRespDTO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/biz/system/oauth2/dto/OAuth2AccessTokenCreateReqDTO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/biz/system/oauth2/dto/OAuth2AccessTokenCreateReqDTO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/biz/system/oauth2/dto/OAuth2AccessTokenRespDTO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/biz/system/oauth2/dto/OAuth2AccessTokenRespDTO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/biz/system/permission/PermissionCommonApi.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/biz/system/permission/PermissionCommonApi.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/biz/system/permission/dto/DeptDataPermissionRespDTO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/biz/system/permission/dto/DeptDataPermissionRespDTO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/biz/system/tenant/TenantCommonApi.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/biz/system/tenant/TenantCommonApi.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/core/ArrayValuable.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/core/ArrayValuable.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/core/KeyValue.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/core/KeyValue.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/enums/CommonStatusEnum.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/enums/CommonStatusEnum.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/enums/DateIntervalEnum.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/enums/DateIntervalEnum.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/enums/DocumentEnum.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/enums/DocumentEnum.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/enums/RpcConstants.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/enums/RpcConstants.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/enums/TerminalEnum.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/enums/TerminalEnum.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/enums/UserTypeEnum.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/enums/UserTypeEnum.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/enums/WebFilterOrderEnum.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/enums/WebFilterOrderEnum.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/exception/ErrorCode.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/exception/ErrorCode.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/exception/ServerException.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/exception/ServerException.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/exception/ServiceException.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/exception/ServiceException.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/exception/enums/GlobalErrorCodeConstants.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/exception/enums/GlobalErrorCodeConstants.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/exception/enums/ServiceErrorCodeRange.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/exception/enums/ServiceErrorCodeRange.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/exception/util/ServiceExceptionUtil.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/exception/util/ServiceExceptionUtil.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/pojo/CommonResult.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/pojo/CommonResult.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/pojo/PageParam.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/pojo/PageParam.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/pojo/PageResult.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/pojo/PageResult.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/pojo/SortablePageParam.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/pojo/SortablePageParam.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/pojo/SortingField.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/pojo/SortingField.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/util/cache/CacheUtils.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/util/cache/CacheUtils.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/util/collection/ArrayUtils.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/util/collection/ArrayUtils.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/util/collection/CollectionUtils.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/util/collection/CollectionUtils.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/util/collection/MapUtils.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/util/collection/MapUtils.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/util/collection/SetUtils.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/util/collection/SetUtils.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/util/date/DateUtils.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/util/date/DateUtils.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/util/date/LocalDateTimeUtils$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/util/date/LocalDateTimeUtils.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/util/date/LocalDateTimeUtils.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/util/http/HttpUtils.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/util/http/HttpUtils.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/util/io/FileUtils.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/util/io/FileUtils.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/util/io/IoUtils.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/util/io/IoUtils.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/util/json/JsonUtils.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/util/json/JsonUtils.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/util/json/databind/NumberSerializer.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/util/json/databind/NumberSerializer.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/util/json/databind/TimestampLocalDateTimeDeserializer.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/util/json/databind/TimestampLocalDateTimeDeserializer.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/util/json/databind/TimestampLocalDateTimeSerializer.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/util/json/databind/TimestampLocalDateTimeSerializer.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/util/monitor/TracerUtils.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/util/monitor/TracerUtils.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/util/number/MoneyUtils.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/util/number/MoneyUtils.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/util/number/NumberUtils.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/util/number/NumberUtils.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/util/object/BeanUtils.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/util/object/BeanUtils.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/util/object/ObjectUtils.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/util/object/ObjectUtils.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/util/object/PageUtils.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/util/object/PageUtils.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/util/servlet/ServletUtils.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/util/servlet/ServletUtils.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/util/spring/SpringExpressionUtils.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/util/spring/SpringExpressionUtils.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/util/spring/SpringUtils.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/util/spring/SpringUtils.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/util/string/StrUtils.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/util/string/StrUtils.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/util/validation/ValidationUtils.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/util/validation/ValidationUtils.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/validation/InEnum.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/validation/InEnum.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/validation/InEnumCollectionValidator.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/validation/InEnumCollectionValidator.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/validation/InEnumValidator.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/validation/InEnumValidator.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/validation/Mobile.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/validation/Mobile.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/validation/MobileValidator.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/validation/MobileValidator.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/validation/Telephone.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/validation/Telephone.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/validation/TelephoneValidator.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-common/target/classes/cn/iocoder/yudao/framework/common/validation/TelephoneValidator.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-data-permission/target/classes/cn/iocoder/yudao/framework/datapermission/config/YudaoDataPermissionAutoConfiguration.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-data-permission/target/classes/cn/iocoder/yudao/framework/datapermission/config/YudaoDataPermissionAutoConfiguration.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-data-permission/target/classes/cn/iocoder/yudao/framework/datapermission/config/YudaoDeptDataPermissionAutoConfiguration.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-data-permission/target/classes/cn/iocoder/yudao/framework/datapermission/config/YudaoDeptDataPermissionAutoConfiguration.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-data-permission/target/classes/cn/iocoder/yudao/framework/datapermission/core/annotation/DataPermission.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-data-permission/target/classes/cn/iocoder/yudao/framework/datapermission/core/annotation/DataPermission.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-data-permission/target/classes/cn/iocoder/yudao/framework/datapermission/core/aop/DataPermissionAnnotationAdvisor.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-data-permission/target/classes/cn/iocoder/yudao/framework/datapermission/core/aop/DataPermissionAnnotationAdvisor.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-data-permission/target/classes/cn/iocoder/yudao/framework/datapermission/core/aop/DataPermissionAnnotationInterceptor.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-data-permission/target/classes/cn/iocoder/yudao/framework/datapermission/core/aop/DataPermissionAnnotationInterceptor.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-data-permission/target/classes/cn/iocoder/yudao/framework/datapermission/core/aop/DataPermissionContextHolder.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-data-permission/target/classes/cn/iocoder/yudao/framework/datapermission/core/aop/DataPermissionContextHolder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-data-permission/target/classes/cn/iocoder/yudao/framework/datapermission/core/db/DataPermissionRuleHandler.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-data-permission/target/classes/cn/iocoder/yudao/framework/datapermission/core/db/DataPermissionRuleHandler.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-data-permission/target/classes/cn/iocoder/yudao/framework/datapermission/core/rule/DataPermissionRule.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-data-permission/target/classes/cn/iocoder/yudao/framework/datapermission/core/rule/DataPermissionRule.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-data-permission/target/classes/cn/iocoder/yudao/framework/datapermission/core/rule/DataPermissionRuleFactory.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-data-permission/target/classes/cn/iocoder/yudao/framework/datapermission/core/rule/DataPermissionRuleFactory.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-data-permission/target/classes/cn/iocoder/yudao/framework/datapermission/core/rule/DataPermissionRuleFactoryImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-data-permission/target/classes/cn/iocoder/yudao/framework/datapermission/core/rule/DataPermissionRuleFactoryImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-data-permission/target/classes/cn/iocoder/yudao/framework/datapermission/core/rule/dept/DeptDataPermissionRule.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-data-permission/target/classes/cn/iocoder/yudao/framework/datapermission/core/rule/dept/DeptDataPermissionRule.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-data-permission/target/classes/cn/iocoder/yudao/framework/datapermission/core/rule/dept/DeptDataPermissionRuleCustomizer.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-data-permission/target/classes/cn/iocoder/yudao/framework/datapermission/core/rule/dept/DeptDataPermissionRuleCustomizer.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-data-permission/target/classes/cn/iocoder/yudao/framework/datapermission/core/util/DataPermissionUtils.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-data-permission/target/classes/cn/iocoder/yudao/framework/datapermission/core/util/DataPermissionUtils.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-ip/target/classes/cn/iocoder/yudao/framework/ip/core/Area.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-ip/target/classes/cn/iocoder/yudao/framework/ip/core/Area.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-ip/target/classes/cn/iocoder/yudao/framework/ip/core/enums/AreaTypeEnum.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-ip/target/classes/cn/iocoder/yudao/framework/ip/core/enums/AreaTypeEnum.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-ip/target/classes/cn/iocoder/yudao/framework/ip/core/utils/AreaUtils.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-ip/target/classes/cn/iocoder/yudao/framework/ip/core/utils/AreaUtils.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-ip/target/classes/cn/iocoder/yudao/framework/ip/core/utils/IPUtils.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-ip/target/classes/cn/iocoder/yudao/framework/ip/core/utils/IPUtils.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-tenant/target/classes/cn/iocoder/yudao/framework/tenant/config/TenantProperties.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-tenant/target/classes/cn/iocoder/yudao/framework/tenant/config/TenantProperties.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-tenant/target/classes/cn/iocoder/yudao/framework/tenant/config/YudaoTenantAutoConfiguration$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-tenant/target/classes/cn/iocoder/yudao/framework/tenant/config/YudaoTenantAutoConfiguration$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-tenant/target/classes/cn/iocoder/yudao/framework/tenant/config/YudaoTenantAutoConfiguration.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-tenant/target/classes/cn/iocoder/yudao/framework/tenant/config/YudaoTenantAutoConfiguration.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-tenant/target/classes/cn/iocoder/yudao/framework/tenant/core/aop/TenantIgnore.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-tenant/target/classes/cn/iocoder/yudao/framework/tenant/core/aop/TenantIgnore.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-tenant/target/classes/cn/iocoder/yudao/framework/tenant/core/aop/TenantIgnoreAspect.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-tenant/target/classes/cn/iocoder/yudao/framework/tenant/core/aop/TenantIgnoreAspect.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-tenant/target/classes/cn/iocoder/yudao/framework/tenant/core/context/TenantContextHolder.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-tenant/target/classes/cn/iocoder/yudao/framework/tenant/core/context/TenantContextHolder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-tenant/target/classes/cn/iocoder/yudao/framework/tenant/core/db/TenantBaseDO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-tenant/target/classes/cn/iocoder/yudao/framework/tenant/core/db/TenantBaseDO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-tenant/target/classes/cn/iocoder/yudao/framework/tenant/core/db/TenantDatabaseInterceptor.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-tenant/target/classes/cn/iocoder/yudao/framework/tenant/core/db/TenantDatabaseInterceptor.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-tenant/target/classes/cn/iocoder/yudao/framework/tenant/core/job/TenantJob.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-tenant/target/classes/cn/iocoder/yudao/framework/tenant/core/job/TenantJob.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-tenant/target/classes/cn/iocoder/yudao/framework/tenant/core/job/TenantJobAspect.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-tenant/target/classes/cn/iocoder/yudao/framework/tenant/core/job/TenantJobAspect.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-tenant/target/classes/cn/iocoder/yudao/framework/tenant/core/mq/kafka/TenantKafkaEnvironmentPostProcessor.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-tenant/target/classes/cn/iocoder/yudao/framework/tenant/core/mq/kafka/TenantKafkaEnvironmentPostProcessor.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-tenant/target/classes/cn/iocoder/yudao/framework/tenant/core/mq/kafka/TenantKafkaProducerInterceptor.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-tenant/target/classes/cn/iocoder/yudao/framework/tenant/core/mq/kafka/TenantKafkaProducerInterceptor.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-tenant/target/classes/cn/iocoder/yudao/framework/tenant/core/mq/rabbitmq/TenantRabbitMQInitializer.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-tenant/target/classes/cn/iocoder/yudao/framework/tenant/core/mq/rabbitmq/TenantRabbitMQInitializer.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-tenant/target/classes/cn/iocoder/yudao/framework/tenant/core/mq/rabbitmq/TenantRabbitMQMessagePostProcessor.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-tenant/target/classes/cn/iocoder/yudao/framework/tenant/core/mq/rabbitmq/TenantRabbitMQMessagePostProcessor.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-tenant/target/classes/cn/iocoder/yudao/framework/tenant/core/mq/redis/TenantRedisMessageInterceptor.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-tenant/target/classes/cn/iocoder/yudao/framework/tenant/core/mq/redis/TenantRedisMessageInterceptor.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-tenant/target/classes/cn/iocoder/yudao/framework/tenant/core/mq/rocketmq/TenantRocketMQConsumeMessageHook.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-tenant/target/classes/cn/iocoder/yudao/framework/tenant/core/mq/rocketmq/TenantRocketMQConsumeMessageHook.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-tenant/target/classes/cn/iocoder/yudao/framework/tenant/core/mq/rocketmq/TenantRocketMQInitializer.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-tenant/target/classes/cn/iocoder/yudao/framework/tenant/core/mq/rocketmq/TenantRocketMQInitializer.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-tenant/target/classes/cn/iocoder/yudao/framework/tenant/core/mq/rocketmq/TenantRocketMQSendMessageHook.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-tenant/target/classes/cn/iocoder/yudao/framework/tenant/core/mq/rocketmq/TenantRocketMQSendMessageHook.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-tenant/target/classes/cn/iocoder/yudao/framework/tenant/core/redis/TenantRedisCacheManager.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-tenant/target/classes/cn/iocoder/yudao/framework/tenant/core/redis/TenantRedisCacheManager.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-tenant/target/classes/cn/iocoder/yudao/framework/tenant/core/security/TenantSecurityWebFilter.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-tenant/target/classes/cn/iocoder/yudao/framework/tenant/core/security/TenantSecurityWebFilter.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-tenant/target/classes/cn/iocoder/yudao/framework/tenant/core/service/TenantFrameworkService.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-tenant/target/classes/cn/iocoder/yudao/framework/tenant/core/service/TenantFrameworkService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-tenant/target/classes/cn/iocoder/yudao/framework/tenant/core/service/TenantFrameworkServiceImpl$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-tenant/target/classes/cn/iocoder/yudao/framework/tenant/core/service/TenantFrameworkServiceImpl$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-tenant/target/classes/cn/iocoder/yudao/framework/tenant/core/service/TenantFrameworkServiceImpl$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-tenant/target/classes/cn/iocoder/yudao/framework/tenant/core/service/TenantFrameworkServiceImpl$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-tenant/target/classes/cn/iocoder/yudao/framework/tenant/core/service/TenantFrameworkServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-tenant/target/classes/cn/iocoder/yudao/framework/tenant/core/service/TenantFrameworkServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-tenant/target/classes/cn/iocoder/yudao/framework/tenant/core/util/TenantUtils.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-tenant/target/classes/cn/iocoder/yudao/framework/tenant/core/util/TenantUtils.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-tenant/target/classes/cn/iocoder/yudao/framework/tenant/core/web/TenantContextWebFilter.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-tenant/target/classes/cn/iocoder/yudao/framework/tenant/core/web/TenantContextWebFilter.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-tenant/target/classes/cn/iocoder/yudao/framework/tenant/core/web/TenantVisitContextInterceptor.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-tenant/target/classes/cn/iocoder/yudao/framework/tenant/core/web/TenantVisitContextInterceptor.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-tenant/target/classes/org/springframework/messaging/handler/invocation/InvocableHandlerMethod$AsyncResultMethodParameter.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-tenant/target/classes/org/springframework/messaging/handler/invocation/InvocableHandlerMethod$AsyncResultMethodParameter.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-tenant/target/classes/org/springframework/messaging/handler/invocation/InvocableHandlerMethod.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-tenant/target/classes/org/springframework/messaging/handler/invocation/InvocableHandlerMethod.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-excel/src/main/java/cn/iocoder/yudao/framework/excel/core/util/ExcelUtils.java" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-excel/src/main/java/cn/iocoder/yudao/framework/excel/core/util/ExcelUtils.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-excel/target/classes/cn/iocoder/yudao/framework/dict/config/YudaoDictAutoConfiguration.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-excel/target/classes/cn/iocoder/yudao/framework/dict/config/YudaoDictAutoConfiguration.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-excel/target/classes/cn/iocoder/yudao/framework/dict/core/DictFrameworkUtils$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-excel/target/classes/cn/iocoder/yudao/framework/dict/core/DictFrameworkUtils$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-excel/target/classes/cn/iocoder/yudao/framework/dict/core/DictFrameworkUtils.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-excel/target/classes/cn/iocoder/yudao/framework/dict/core/DictFrameworkUtils.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-excel/target/classes/cn/iocoder/yudao/framework/excel/core/annotations/DictFormat.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-excel/target/classes/cn/iocoder/yudao/framework/excel/core/annotations/DictFormat.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-excel/target/classes/cn/iocoder/yudao/framework/excel/core/annotations/ExcelColumnSelect.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-excel/target/classes/cn/iocoder/yudao/framework/excel/core/annotations/ExcelColumnSelect.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-excel/target/classes/cn/iocoder/yudao/framework/excel/core/convert/AreaConvert.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-excel/target/classes/cn/iocoder/yudao/framework/excel/core/convert/AreaConvert.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-excel/target/classes/cn/iocoder/yudao/framework/excel/core/convert/DictConvert.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-excel/target/classes/cn/iocoder/yudao/framework/excel/core/convert/DictConvert.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-excel/target/classes/cn/iocoder/yudao/framework/excel/core/convert/JsonConvert.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-excel/target/classes/cn/iocoder/yudao/framework/excel/core/convert/JsonConvert.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-excel/target/classes/cn/iocoder/yudao/framework/excel/core/convert/MoneyConvert.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-excel/target/classes/cn/iocoder/yudao/framework/excel/core/convert/MoneyConvert.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-excel/target/classes/cn/iocoder/yudao/framework/excel/core/function/ExcelColumnSelectFunction.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-excel/target/classes/cn/iocoder/yudao/framework/excel/core/function/ExcelColumnSelectFunction.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-excel/target/classes/cn/iocoder/yudao/framework/excel/core/handler/SelectSheetWriteHandler.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-excel/target/classes/cn/iocoder/yudao/framework/excel/core/handler/SelectSheetWriteHandler.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-excel/target/classes/cn/iocoder/yudao/framework/excel/core/util/ExcelUtils.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-excel/target/classes/cn/iocoder/yudao/framework/excel/core/util/ExcelUtils.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-job/target/classes/cn/iocoder/yudao/framework/quartz/config/YudaoAsyncAutoConfiguration$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-job/target/classes/cn/iocoder/yudao/framework/quartz/config/YudaoAsyncAutoConfiguration$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-job/target/classes/cn/iocoder/yudao/framework/quartz/config/YudaoAsyncAutoConfiguration.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-job/target/classes/cn/iocoder/yudao/framework/quartz/config/YudaoAsyncAutoConfiguration.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-job/target/classes/cn/iocoder/yudao/framework/quartz/config/YudaoQuartzAutoConfiguration.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-job/target/classes/cn/iocoder/yudao/framework/quartz/config/YudaoQuartzAutoConfiguration.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-job/target/classes/cn/iocoder/yudao/framework/quartz/core/enums/JobDataKeyEnum.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-job/target/classes/cn/iocoder/yudao/framework/quartz/core/enums/JobDataKeyEnum.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-job/target/classes/cn/iocoder/yudao/framework/quartz/core/handler/JobHandler.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-job/target/classes/cn/iocoder/yudao/framework/quartz/core/handler/JobHandler.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-job/target/classes/cn/iocoder/yudao/framework/quartz/core/handler/JobHandlerInvoker.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-job/target/classes/cn/iocoder/yudao/framework/quartz/core/handler/JobHandlerInvoker.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-job/target/classes/cn/iocoder/yudao/framework/quartz/core/scheduler/SchedulerManager.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-job/target/classes/cn/iocoder/yudao/framework/quartz/core/scheduler/SchedulerManager.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-job/target/classes/cn/iocoder/yudao/framework/quartz/core/service/JobLogFrameworkService.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-job/target/classes/cn/iocoder/yudao/framework/quartz/core/service/JobLogFrameworkService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-job/target/classes/cn/iocoder/yudao/framework/quartz/core/util/CronUtils.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-job/target/classes/cn/iocoder/yudao/framework/quartz/core/util/CronUtils.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-monitor/target/classes/cn/iocoder/yudao/framework/tracer/config/TracerProperties.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-monitor/target/classes/cn/iocoder/yudao/framework/tracer/config/TracerProperties.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-monitor/target/classes/cn/iocoder/yudao/framework/tracer/config/YudaoMetricsAutoConfiguration.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-monitor/target/classes/cn/iocoder/yudao/framework/tracer/config/YudaoMetricsAutoConfiguration.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-monitor/target/classes/cn/iocoder/yudao/framework/tracer/config/YudaoTracerAutoConfiguration.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-monitor/target/classes/cn/iocoder/yudao/framework/tracer/config/YudaoTracerAutoConfiguration.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-monitor/target/classes/cn/iocoder/yudao/framework/tracer/core/annotation/BizTrace.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-monitor/target/classes/cn/iocoder/yudao/framework/tracer/core/annotation/BizTrace.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-monitor/target/classes/cn/iocoder/yudao/framework/tracer/core/aop/BizTraceAspect.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-monitor/target/classes/cn/iocoder/yudao/framework/tracer/core/aop/BizTraceAspect.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-monitor/target/classes/cn/iocoder/yudao/framework/tracer/core/filter/TraceFilter.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-monitor/target/classes/cn/iocoder/yudao/framework/tracer/core/filter/TraceFilter.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-monitor/target/classes/cn/iocoder/yudao/framework/tracer/core/util/TracerFrameworkUtils.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-monitor/target/classes/cn/iocoder/yudao/framework/tracer/core/util/TracerFrameworkUtils.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-mq/target/classes/cn/iocoder/yudao/framework/mq/rabbitmq/config/YudaoRabbitMQAutoConfiguration.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-mq/target/classes/cn/iocoder/yudao/framework/mq/rabbitmq/config/YudaoRabbitMQAutoConfiguration.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-mq/target/classes/cn/iocoder/yudao/framework/mq/redis/config/YudaoRedisMQConsumerAutoConfiguration.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-mq/target/classes/cn/iocoder/yudao/framework/mq/redis/config/YudaoRedisMQConsumerAutoConfiguration.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-mq/target/classes/cn/iocoder/yudao/framework/mq/redis/config/YudaoRedisMQProducerAutoConfiguration.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-mq/target/classes/cn/iocoder/yudao/framework/mq/redis/config/YudaoRedisMQProducerAutoConfiguration.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-mq/target/classes/cn/iocoder/yudao/framework/mq/redis/core/RedisMQTemplate.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-mq/target/classes/cn/iocoder/yudao/framework/mq/redis/core/RedisMQTemplate.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-mq/target/classes/cn/iocoder/yudao/framework/mq/redis/core/interceptor/RedisMessageInterceptor.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-mq/target/classes/cn/iocoder/yudao/framework/mq/redis/core/interceptor/RedisMessageInterceptor.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-mq/target/classes/cn/iocoder/yudao/framework/mq/redis/core/job/RedisPendingMessageResendJob.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-mq/target/classes/cn/iocoder/yudao/framework/mq/redis/core/job/RedisPendingMessageResendJob.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-mq/target/classes/cn/iocoder/yudao/framework/mq/redis/core/job/RedisStreamMessageCleanupJob.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-mq/target/classes/cn/iocoder/yudao/framework/mq/redis/core/job/RedisStreamMessageCleanupJob.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-mq/target/classes/cn/iocoder/yudao/framework/mq/redis/core/message/AbstractRedisMessage.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-mq/target/classes/cn/iocoder/yudao/framework/mq/redis/core/message/AbstractRedisMessage.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-mq/target/classes/cn/iocoder/yudao/framework/mq/redis/core/pubsub/AbstractRedisChannelMessage.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-mq/target/classes/cn/iocoder/yudao/framework/mq/redis/core/pubsub/AbstractRedisChannelMessage.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-mq/target/classes/cn/iocoder/yudao/framework/mq/redis/core/pubsub/AbstractRedisChannelMessageListener.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-mq/target/classes/cn/iocoder/yudao/framework/mq/redis/core/pubsub/AbstractRedisChannelMessageListener.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-mq/target/classes/cn/iocoder/yudao/framework/mq/redis/core/stream/AbstractRedisStreamMessage.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-mq/target/classes/cn/iocoder/yudao/framework/mq/redis/core/stream/AbstractRedisStreamMessage.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-mq/target/classes/cn/iocoder/yudao/framework/mq/redis/core/stream/AbstractRedisStreamMessageListener.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-mq/target/classes/cn/iocoder/yudao/framework/mq/redis/core/stream/AbstractRedisStreamMessageListener.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-mybatis/target/classes/cn/iocoder/yudao/framework/datasource/config/YudaoDataSourceAutoConfiguration.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-mybatis/target/classes/cn/iocoder/yudao/framework/datasource/config/YudaoDataSourceAutoConfiguration.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-mybatis/target/classes/cn/iocoder/yudao/framework/datasource/core/enums/DataSourceEnum.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-mybatis/target/classes/cn/iocoder/yudao/framework/datasource/core/enums/DataSourceEnum.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-mybatis/target/classes/cn/iocoder/yudao/framework/datasource/core/filter/DruidAdRemoveFilter.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-mybatis/target/classes/cn/iocoder/yudao/framework/datasource/core/filter/DruidAdRemoveFilter.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-mybatis/target/classes/cn/iocoder/yudao/framework/mybatis/config/IdTypeEnvironmentPostProcessor$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-mybatis/target/classes/cn/iocoder/yudao/framework/mybatis/config/IdTypeEnvironmentPostProcessor.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-mybatis/target/classes/cn/iocoder/yudao/framework/mybatis/config/IdTypeEnvironmentPostProcessor.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-mybatis/target/classes/cn/iocoder/yudao/framework/mybatis/config/YudaoMybatisAutoConfiguration$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-mybatis/target/classes/cn/iocoder/yudao/framework/mybatis/config/YudaoMybatisAutoConfiguration.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-mybatis/target/classes/cn/iocoder/yudao/framework/mybatis/config/YudaoMybatisAutoConfiguration.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-mybatis/target/classes/cn/iocoder/yudao/framework/mybatis/core/dataobject/BaseDO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-mybatis/target/classes/cn/iocoder/yudao/framework/mybatis/core/dataobject/BaseDO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-mybatis/target/classes/cn/iocoder/yudao/framework/mybatis/core/enums/DbTypeEnum.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-mybatis/target/classes/cn/iocoder/yudao/framework/mybatis/core/enums/DbTypeEnum.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-mybatis/target/classes/cn/iocoder/yudao/framework/mybatis/core/handler/DefaultDBFieldHandler.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-mybatis/target/classes/cn/iocoder/yudao/framework/mybatis/core/handler/DefaultDBFieldHandler.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-mybatis/target/classes/cn/iocoder/yudao/framework/mybatis/core/mapper/BaseMapperX.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-mybatis/target/classes/cn/iocoder/yudao/framework/mybatis/core/mapper/BaseMapperX.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-mybatis/target/classes/cn/iocoder/yudao/framework/mybatis/core/query/LambdaQueryWrapperX.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-mybatis/target/classes/cn/iocoder/yudao/framework/mybatis/core/query/LambdaQueryWrapperX.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-mybatis/target/classes/cn/iocoder/yudao/framework/mybatis/core/query/MPJLambdaWrapperX.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-mybatis/target/classes/cn/iocoder/yudao/framework/mybatis/core/query/MPJLambdaWrapperX.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-mybatis/target/classes/cn/iocoder/yudao/framework/mybatis/core/query/QueryWrapperX$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-mybatis/target/classes/cn/iocoder/yudao/framework/mybatis/core/query/QueryWrapperX.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-mybatis/target/classes/cn/iocoder/yudao/framework/mybatis/core/query/QueryWrapperX.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-mybatis/target/classes/cn/iocoder/yudao/framework/mybatis/core/type/EncryptTypeHandler.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-mybatis/target/classes/cn/iocoder/yudao/framework/mybatis/core/type/EncryptTypeHandler.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-mybatis/target/classes/cn/iocoder/yudao/framework/mybatis/core/type/IntegerListTypeHandler.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-mybatis/target/classes/cn/iocoder/yudao/framework/mybatis/core/type/IntegerListTypeHandler.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-mybatis/target/classes/cn/iocoder/yudao/framework/mybatis/core/type/LongListTypeHandler.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-mybatis/target/classes/cn/iocoder/yudao/framework/mybatis/core/type/LongListTypeHandler.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-mybatis/target/classes/cn/iocoder/yudao/framework/mybatis/core/type/LongSetTypeHandler.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-mybatis/target/classes/cn/iocoder/yudao/framework/mybatis/core/type/LongSetTypeHandler.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-mybatis/target/classes/cn/iocoder/yudao/framework/mybatis/core/type/StringListTypeHandler.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-mybatis/target/classes/cn/iocoder/yudao/framework/mybatis/core/type/StringListTypeHandler.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-mybatis/target/classes/cn/iocoder/yudao/framework/mybatis/core/util/JdbcUtils.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-mybatis/target/classes/cn/iocoder/yudao/framework/mybatis/core/util/JdbcUtils.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-mybatis/target/classes/cn/iocoder/yudao/framework/mybatis/core/util/MyBatisUtils.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-mybatis/target/classes/cn/iocoder/yudao/framework/mybatis/core/util/MyBatisUtils.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-mybatis/target/classes/cn/iocoder/yudao/framework/translate/config/YudaoTranslateAutoConfiguration.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-mybatis/target/classes/cn/iocoder/yudao/framework/translate/config/YudaoTranslateAutoConfiguration.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-mybatis/target/classes/cn/iocoder/yudao/framework/translate/core/TranslateUtils.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-mybatis/target/classes/cn/iocoder/yudao/framework/translate/core/TranslateUtils.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-protection/target/classes/cn/iocoder/yudao/framework/idempotent/config/YudaoIdempotentConfiguration.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-protection/target/classes/cn/iocoder/yudao/framework/idempotent/config/YudaoIdempotentConfiguration.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-protection/target/classes/cn/iocoder/yudao/framework/idempotent/core/annotation/Idempotent.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-protection/target/classes/cn/iocoder/yudao/framework/idempotent/core/annotation/Idempotent.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-protection/target/classes/cn/iocoder/yudao/framework/idempotent/core/aop/IdempotentAspect.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-protection/target/classes/cn/iocoder/yudao/framework/idempotent/core/aop/IdempotentAspect.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-protection/target/classes/cn/iocoder/yudao/framework/idempotent/core/keyresolver/IdempotentKeyResolver.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-protection/target/classes/cn/iocoder/yudao/framework/idempotent/core/keyresolver/IdempotentKeyResolver.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-protection/target/classes/cn/iocoder/yudao/framework/idempotent/core/keyresolver/impl/DefaultIdempotentKeyResolver.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-protection/target/classes/cn/iocoder/yudao/framework/idempotent/core/keyresolver/impl/DefaultIdempotentKeyResolver.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-protection/target/classes/cn/iocoder/yudao/framework/idempotent/core/keyresolver/impl/ExpressionIdempotentKeyResolver.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-protection/target/classes/cn/iocoder/yudao/framework/idempotent/core/keyresolver/impl/ExpressionIdempotentKeyResolver.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-protection/target/classes/cn/iocoder/yudao/framework/idempotent/core/keyresolver/impl/UserIdempotentKeyResolver.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-protection/target/classes/cn/iocoder/yudao/framework/idempotent/core/keyresolver/impl/UserIdempotentKeyResolver.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-protection/target/classes/cn/iocoder/yudao/framework/idempotent/core/redis/IdempotentRedisDAO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-protection/target/classes/cn/iocoder/yudao/framework/idempotent/core/redis/IdempotentRedisDAO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-protection/target/classes/cn/iocoder/yudao/framework/lock4j/config/YudaoLock4jConfiguration.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-protection/target/classes/cn/iocoder/yudao/framework/lock4j/config/YudaoLock4jConfiguration.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-protection/target/classes/cn/iocoder/yudao/framework/lock4j/core/DefaultLockFailureStrategy.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-protection/target/classes/cn/iocoder/yudao/framework/lock4j/core/DefaultLockFailureStrategy.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-protection/target/classes/cn/iocoder/yudao/framework/lock4j/core/Lock4jRedisKeyConstants.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-protection/target/classes/cn/iocoder/yudao/framework/lock4j/core/Lock4jRedisKeyConstants.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-protection/target/classes/cn/iocoder/yudao/framework/ratelimiter/config/YudaoRateLimiterConfiguration.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-protection/target/classes/cn/iocoder/yudao/framework/ratelimiter/config/YudaoRateLimiterConfiguration.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-protection/target/classes/cn/iocoder/yudao/framework/ratelimiter/core/annotation/RateLimiter.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-protection/target/classes/cn/iocoder/yudao/framework/ratelimiter/core/annotation/RateLimiter.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-protection/target/classes/cn/iocoder/yudao/framework/ratelimiter/core/aop/RateLimiterAspect.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-protection/target/classes/cn/iocoder/yudao/framework/ratelimiter/core/aop/RateLimiterAspect.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-protection/target/classes/cn/iocoder/yudao/framework/ratelimiter/core/keyresolver/RateLimiterKeyResolver.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-protection/target/classes/cn/iocoder/yudao/framework/ratelimiter/core/keyresolver/RateLimiterKeyResolver.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-protection/target/classes/cn/iocoder/yudao/framework/ratelimiter/core/keyresolver/impl/ClientIpRateLimiterKeyResolver.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-protection/target/classes/cn/iocoder/yudao/framework/ratelimiter/core/keyresolver/impl/ClientIpRateLimiterKeyResolver.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-protection/target/classes/cn/iocoder/yudao/framework/ratelimiter/core/keyresolver/impl/DefaultRateLimiterKeyResolver.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-protection/target/classes/cn/iocoder/yudao/framework/ratelimiter/core/keyresolver/impl/DefaultRateLimiterKeyResolver.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-protection/target/classes/cn/iocoder/yudao/framework/ratelimiter/core/keyresolver/impl/ExpressionRateLimiterKeyResolver.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-protection/target/classes/cn/iocoder/yudao/framework/ratelimiter/core/keyresolver/impl/ExpressionRateLimiterKeyResolver.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-protection/target/classes/cn/iocoder/yudao/framework/ratelimiter/core/keyresolver/impl/ServerNodeRateLimiterKeyResolver.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-protection/target/classes/cn/iocoder/yudao/framework/ratelimiter/core/keyresolver/impl/ServerNodeRateLimiterKeyResolver.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-protection/target/classes/cn/iocoder/yudao/framework/ratelimiter/core/keyresolver/impl/UserRateLimiterKeyResolver.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-protection/target/classes/cn/iocoder/yudao/framework/ratelimiter/core/keyresolver/impl/UserRateLimiterKeyResolver.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-protection/target/classes/cn/iocoder/yudao/framework/ratelimiter/core/redis/RateLimiterRedisDAO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-protection/target/classes/cn/iocoder/yudao/framework/ratelimiter/core/redis/RateLimiterRedisDAO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-protection/target/classes/cn/iocoder/yudao/framework/signature/config/YudaoApiSignatureAutoConfiguration.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-protection/target/classes/cn/iocoder/yudao/framework/signature/config/YudaoApiSignatureAutoConfiguration.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-protection/target/classes/cn/iocoder/yudao/framework/signature/core/annotation/ApiSignature.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-protection/target/classes/cn/iocoder/yudao/framework/signature/core/annotation/ApiSignature.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-protection/target/classes/cn/iocoder/yudao/framework/signature/core/aop/ApiSignatureAspect.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-protection/target/classes/cn/iocoder/yudao/framework/signature/core/aop/ApiSignatureAspect.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-protection/target/classes/cn/iocoder/yudao/framework/signature/core/redis/ApiSignatureRedisDAO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-protection/target/classes/cn/iocoder/yudao/framework/signature/core/redis/ApiSignatureRedisDAO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-redis/target/classes/cn/iocoder/yudao/framework/redis/config/YudaoCacheAutoConfiguration.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-redis/target/classes/cn/iocoder/yudao/framework/redis/config/YudaoCacheAutoConfiguration.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-redis/target/classes/cn/iocoder/yudao/framework/redis/config/YudaoCacheProperties.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-redis/target/classes/cn/iocoder/yudao/framework/redis/config/YudaoCacheProperties.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-redis/target/classes/cn/iocoder/yudao/framework/redis/config/YudaoRedisAutoConfiguration.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-redis/target/classes/cn/iocoder/yudao/framework/redis/config/YudaoRedisAutoConfiguration.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-redis/target/classes/cn/iocoder/yudao/framework/redis/core/TimeoutRedisCacheManager.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-redis/target/classes/cn/iocoder/yudao/framework/redis/core/TimeoutRedisCacheManager.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-security/target/classes/cn/iocoder/yudao/framework/operatelog/config/YudaoOperateLogConfiguration.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-security/target/classes/cn/iocoder/yudao/framework/operatelog/config/YudaoOperateLogConfiguration.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-security/target/classes/cn/iocoder/yudao/framework/operatelog/core/service/LogRecordServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-security/target/classes/cn/iocoder/yudao/framework/operatelog/core/service/LogRecordServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-security/target/classes/cn/iocoder/yudao/framework/security/config/AuthorizeRequestsCustomizer.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-security/target/classes/cn/iocoder/yudao/framework/security/config/AuthorizeRequestsCustomizer.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-security/target/classes/cn/iocoder/yudao/framework/security/config/SecurityProperties.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-security/target/classes/cn/iocoder/yudao/framework/security/config/SecurityProperties.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-security/target/classes/cn/iocoder/yudao/framework/security/config/YudaoSecurityAutoConfiguration.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-security/target/classes/cn/iocoder/yudao/framework/security/config/YudaoSecurityAutoConfiguration.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-security/target/classes/cn/iocoder/yudao/framework/security/config/YudaoWebSecurityConfigurerAdapter$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-security/target/classes/cn/iocoder/yudao/framework/security/config/YudaoWebSecurityConfigurerAdapter.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-security/target/classes/cn/iocoder/yudao/framework/security/config/YudaoWebSecurityConfigurerAdapter.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-security/target/classes/cn/iocoder/yudao/framework/security/core/LoginUser.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-security/target/classes/cn/iocoder/yudao/framework/security/core/LoginUser.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-security/target/classes/cn/iocoder/yudao/framework/security/core/context/TransmittableThreadLocalSecurityContextHolderStrategy.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-security/target/classes/cn/iocoder/yudao/framework/security/core/context/TransmittableThreadLocalSecurityContextHolderStrategy.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-security/target/classes/cn/iocoder/yudao/framework/security/core/filter/TokenAuthenticationFilter.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-security/target/classes/cn/iocoder/yudao/framework/security/core/filter/TokenAuthenticationFilter.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-security/target/classes/cn/iocoder/yudao/framework/security/core/handler/AccessDeniedHandlerImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-security/target/classes/cn/iocoder/yudao/framework/security/core/handler/AccessDeniedHandlerImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-security/target/classes/cn/iocoder/yudao/framework/security/core/handler/AuthenticationEntryPointImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-security/target/classes/cn/iocoder/yudao/framework/security/core/handler/AuthenticationEntryPointImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-security/target/classes/cn/iocoder/yudao/framework/security/core/service/SecurityFrameworkService.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-security/target/classes/cn/iocoder/yudao/framework/security/core/service/SecurityFrameworkService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-security/target/classes/cn/iocoder/yudao/framework/security/core/service/SecurityFrameworkServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-security/target/classes/cn/iocoder/yudao/framework/security/core/service/SecurityFrameworkServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-security/target/classes/cn/iocoder/yudao/framework/security/core/util/SecurityFrameworkUtils.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-security/target/classes/cn/iocoder/yudao/framework/security/core/util/SecurityFrameworkUtils.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/META-INF/spring-configuration-metadata.json" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/META-INF/spring-configuration-metadata.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/apilog/config/YudaoApiLogAutoConfiguration.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/apilog/config/YudaoApiLogAutoConfiguration.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/apilog/core/annotation/ApiAccessLog.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/apilog/core/annotation/ApiAccessLog.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/apilog/core/enums/OperateTypeEnum.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/apilog/core/enums/OperateTypeEnum.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/apilog/core/filter/ApiAccessLogFilter$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/apilog/core/filter/ApiAccessLogFilter.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/apilog/core/filter/ApiAccessLogFilter.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/apilog/core/interceptor/ApiAccessLogInterceptor.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/apilog/core/interceptor/ApiAccessLogInterceptor.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/banner/config/YudaoBannerAutoConfiguration.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/banner/config/YudaoBannerAutoConfiguration.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/banner/core/BannerApplicationRunner.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/banner/core/BannerApplicationRunner.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/desensitize/core/base/annotation/DesensitizeBy.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/desensitize/core/base/annotation/DesensitizeBy.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/desensitize/core/base/handler/DesensitizationHandler.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/desensitize/core/base/handler/DesensitizationHandler.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/desensitize/core/base/serializer/StringDesensitizeSerializer.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/desensitize/core/base/serializer/StringDesensitizeSerializer.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/desensitize/core/regex/annotation/EmailDesensitize.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/desensitize/core/regex/annotation/EmailDesensitize.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/desensitize/core/regex/annotation/RegexDesensitize.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/desensitize/core/regex/annotation/RegexDesensitize.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/desensitize/core/regex/handler/AbstractRegexDesensitizationHandler.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/desensitize/core/regex/handler/AbstractRegexDesensitizationHandler.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/desensitize/core/regex/handler/DefaultRegexDesensitizationHandler.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/desensitize/core/regex/handler/DefaultRegexDesensitizationHandler.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/desensitize/core/regex/handler/EmailDesensitizationHandler.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/desensitize/core/regex/handler/EmailDesensitizationHandler.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/desensitize/core/slider/annotation/BankCardDesensitize.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/desensitize/core/slider/annotation/BankCardDesensitize.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/desensitize/core/slider/annotation/CarLicenseDesensitize.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/desensitize/core/slider/annotation/CarLicenseDesensitize.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/desensitize/core/slider/annotation/ChineseNameDesensitize.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/desensitize/core/slider/annotation/ChineseNameDesensitize.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/desensitize/core/slider/annotation/FixedPhoneDesensitize.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/desensitize/core/slider/annotation/FixedPhoneDesensitize.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/desensitize/core/slider/annotation/IdCardDesensitize.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/desensitize/core/slider/annotation/IdCardDesensitize.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/desensitize/core/slider/annotation/MobileDesensitize.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/desensitize/core/slider/annotation/MobileDesensitize.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/desensitize/core/slider/annotation/PasswordDesensitize.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/desensitize/core/slider/annotation/PasswordDesensitize.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/desensitize/core/slider/annotation/SliderDesensitize.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/desensitize/core/slider/annotation/SliderDesensitize.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/desensitize/core/slider/handler/AbstractSliderDesensitizationHandler.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/desensitize/core/slider/handler/AbstractSliderDesensitizationHandler.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/desensitize/core/slider/handler/BankCardDesensitization.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/desensitize/core/slider/handler/BankCardDesensitization.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/desensitize/core/slider/handler/CarLicenseDesensitization.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/desensitize/core/slider/handler/CarLicenseDesensitization.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/desensitize/core/slider/handler/ChineseNameDesensitization.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/desensitize/core/slider/handler/ChineseNameDesensitization.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/desensitize/core/slider/handler/DefaultDesensitizationHandler.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/desensitize/core/slider/handler/DefaultDesensitizationHandler.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/desensitize/core/slider/handler/FixedPhoneDesensitization.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/desensitize/core/slider/handler/FixedPhoneDesensitization.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/desensitize/core/slider/handler/IdCardDesensitization.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/desensitize/core/slider/handler/IdCardDesensitization.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/desensitize/core/slider/handler/MobileDesensitization.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/desensitize/core/slider/handler/MobileDesensitization.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/desensitize/core/slider/handler/PasswordDesensitization.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/desensitize/core/slider/handler/PasswordDesensitization.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/jackson/config/YudaoJacksonAutoConfiguration.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/jackson/config/YudaoJacksonAutoConfiguration.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/swagger/config/SwaggerProperties.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/swagger/config/SwaggerProperties.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/swagger/config/YudaoSwaggerAutoConfiguration.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/swagger/config/YudaoSwaggerAutoConfiguration.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/web/config/WebProperties$Api.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/web/config/WebProperties$Api.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/web/config/WebProperties$Ui.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/web/config/WebProperties$Ui.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/web/config/WebProperties.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/web/config/WebProperties.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/web/config/YudaoWebAutoConfiguration.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/web/config/YudaoWebAutoConfiguration.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/web/core/filter/ApiRequestFilter.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/web/core/filter/ApiRequestFilter.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/web/core/filter/CacheRequestBodyFilter.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/web/core/filter/CacheRequestBodyFilter.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/web/core/filter/CacheRequestBodyWrapper$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/web/core/filter/CacheRequestBodyWrapper$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/web/core/filter/CacheRequestBodyWrapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/web/core/filter/CacheRequestBodyWrapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/web/core/filter/DemoFilter.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/web/core/filter/DemoFilter.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/web/core/handler/GlobalExceptionHandler.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/web/core/handler/GlobalExceptionHandler.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/web/core/handler/GlobalResponseBodyHandler.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/web/core/handler/GlobalResponseBodyHandler.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/web/core/util/WebFrameworkUtils.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/web/core/util/WebFrameworkUtils.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/xss/config/XssProperties.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/xss/config/XssProperties.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/xss/config/YudaoXssAutoConfiguration.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/xss/config/YudaoXssAutoConfiguration.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/xss/core/clean/JsoupXssCleaner.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/xss/core/clean/JsoupXssCleaner.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/xss/core/clean/XssCleaner.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/xss/core/clean/XssCleaner.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/xss/core/filter/XssFilter.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/xss/core/filter/XssFilter.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/xss/core/filter/XssRequestWrapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/xss/core/filter/XssRequestWrapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/xss/core/json/XssStringJsonDeserializer.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/cn/iocoder/yudao/framework/xss/core/json/XssStringJsonDeserializer.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/config/WebSocketProperties.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/config/WebSocketProperties.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/config/YudaoWebSocketAutoConfiguration$KafkaWebSocketMessageSenderConfiguration.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/config/YudaoWebSocketAutoConfiguration$KafkaWebSocketMessageSenderConfiguration.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/config/YudaoWebSocketAutoConfiguration$LocalWebSocketMessageSenderConfiguration.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/config/YudaoWebSocketAutoConfiguration$LocalWebSocketMessageSenderConfiguration.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/config/YudaoWebSocketAutoConfiguration$RabbitMQWebSocketMessageSenderConfiguration.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/config/YudaoWebSocketAutoConfiguration$RabbitMQWebSocketMessageSenderConfiguration.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/config/YudaoWebSocketAutoConfiguration$RedisWebSocketMessageSenderConfiguration.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/config/YudaoWebSocketAutoConfiguration$RedisWebSocketMessageSenderConfiguration.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/config/YudaoWebSocketAutoConfiguration$RocketMQWebSocketMessageSenderConfiguration.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/config/YudaoWebSocketAutoConfiguration$RocketMQWebSocketMessageSenderConfiguration.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/config/YudaoWebSocketAutoConfiguration.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/config/YudaoWebSocketAutoConfiguration.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/core/handler/JsonWebSocketMessageHandler.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/core/handler/JsonWebSocketMessageHandler.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/core/listener/WebSocketMessageListener.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/core/listener/WebSocketMessageListener.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/core/message/JsonWebSocketMessage.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/core/message/JsonWebSocketMessage.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/core/security/LoginUserHandshakeInterceptor.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/core/security/LoginUserHandshakeInterceptor.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/core/security/WebSocketAuthorizeRequestsCustomizer.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/core/security/WebSocketAuthorizeRequestsCustomizer.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/core/sender/AbstractWebSocketMessageSender.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/core/sender/AbstractWebSocketMessageSender.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/core/sender/WebSocketMessageSender.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/core/sender/WebSocketMessageSender.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/core/sender/kafka/KafkaWebSocketMessage.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/core/sender/kafka/KafkaWebSocketMessage.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/core/sender/kafka/KafkaWebSocketMessageConsumer.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/core/sender/kafka/KafkaWebSocketMessageConsumer.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/core/sender/kafka/KafkaWebSocketMessageSender.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/core/sender/kafka/KafkaWebSocketMessageSender.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/core/sender/local/LocalWebSocketMessageSender.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/core/sender/local/LocalWebSocketMessageSender.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/core/sender/rabbitmq/RabbitMQWebSocketMessage.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/core/sender/rabbitmq/RabbitMQWebSocketMessage.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/core/sender/rabbitmq/RabbitMQWebSocketMessageConsumer.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/core/sender/rabbitmq/RabbitMQWebSocketMessageConsumer.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/core/sender/rabbitmq/RabbitMQWebSocketMessageSender.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/core/sender/rabbitmq/RabbitMQWebSocketMessageSender.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/core/sender/redis/RedisWebSocketMessage.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/core/sender/redis/RedisWebSocketMessage.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/core/sender/redis/RedisWebSocketMessageConsumer.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/core/sender/redis/RedisWebSocketMessageConsumer.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/core/sender/redis/RedisWebSocketMessageSender.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/core/sender/redis/RedisWebSocketMessageSender.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/core/sender/rocketmq/RocketMQWebSocketMessage.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/core/sender/rocketmq/RocketMQWebSocketMessage.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/core/sender/rocketmq/RocketMQWebSocketMessageConsumer.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/core/sender/rocketmq/RocketMQWebSocketMessageConsumer.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/core/sender/rocketmq/RocketMQWebSocketMessageSender.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/core/sender/rocketmq/RocketMQWebSocketMessageSender.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/core/session/WebSocketSessionHandlerDecorator.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/core/session/WebSocketSessionHandlerDecorator.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/core/session/WebSocketSessionManager.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/core/session/WebSocketSessionManager.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/core/session/WebSocketSessionManagerImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/core/session/WebSocketSessionManagerImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/core/util/WebSocketFrameworkUtils.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/core/util/WebSocketFrameworkUtils.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/api/config/ConfigApi.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/api/config/ConfigApi.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/api/config/ConfigApiImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/api/config/ConfigApiImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/api/file/FileApi.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/api/file/FileApi.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/api/file/FileApiImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/api/file/FileApiImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/api/logger/ApiAccessLogApiImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/api/logger/ApiAccessLogApiImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/api/logger/ApiErrorLogApiImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/api/logger/ApiErrorLogApiImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/api/websocket/WebSocketSenderApi.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/api/websocket/WebSocketSenderApi.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/api/websocket/WebSocketSenderApiImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/api/websocket/WebSocketSenderApiImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/codegen/CodegenController.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/codegen/CodegenController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/codegen/vo/CodegenCreateListReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/codegen/vo/CodegenCreateListReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/codegen/vo/CodegenDetailRespVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/codegen/vo/CodegenDetailRespVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/codegen/vo/CodegenPreviewRespVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/codegen/vo/CodegenPreviewRespVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/codegen/vo/CodegenUpdateReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/codegen/vo/CodegenUpdateReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/codegen/vo/column/CodegenColumnRespVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/codegen/vo/column/CodegenColumnRespVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/codegen/vo/column/CodegenColumnSaveReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/codegen/vo/column/CodegenColumnSaveReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/codegen/vo/table/CodegenTablePageReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/codegen/vo/table/CodegenTablePageReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/codegen/vo/table/CodegenTableRespVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/codegen/vo/table/CodegenTableRespVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/codegen/vo/table/CodegenTableSaveReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/codegen/vo/table/CodegenTableSaveReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/codegen/vo/table/DatabaseTableRespVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/codegen/vo/table/DatabaseTableRespVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/config/ConfigController.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/config/ConfigController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/config/vo/ConfigPageReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/config/vo/ConfigPageReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/config/vo/ConfigRespVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/config/vo/ConfigRespVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/config/vo/ConfigSaveReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/config/vo/ConfigSaveReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/db/DataSourceConfigController.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/db/DataSourceConfigController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/db/vo/DataSourceConfigRespVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/db/vo/DataSourceConfigRespVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/db/vo/DataSourceConfigSaveReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/db/vo/DataSourceConfigSaveReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/demo/demo01/Demo01ContactController.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/demo/demo01/Demo01ContactController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/demo/demo01/vo/Demo01ContactPageReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/demo/demo01/vo/Demo01ContactPageReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/demo/demo01/vo/Demo01ContactRespVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/demo/demo01/vo/Demo01ContactRespVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/demo/demo01/vo/Demo01ContactSaveReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/demo/demo01/vo/Demo01ContactSaveReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/demo/demo02/Demo02CategoryController.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/demo/demo02/Demo02CategoryController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/demo/demo02/vo/Demo02CategoryListReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/demo/demo02/vo/Demo02CategoryListReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/demo/demo02/vo/Demo02CategoryRespVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/demo/demo02/vo/Demo02CategoryRespVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/demo/demo02/vo/Demo02CategorySaveReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/demo/demo02/vo/Demo02CategorySaveReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/demo/demo03/erp/Demo03StudentErpController.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/demo/demo03/erp/Demo03StudentErpController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/demo/demo03/erp/vo/Demo03StudentErpPageReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/demo/demo03/erp/vo/Demo03StudentErpPageReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/demo/demo03/erp/vo/Demo03StudentErpRespVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/demo/demo03/erp/vo/Demo03StudentErpRespVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/demo/demo03/erp/vo/Demo03StudentErpSaveReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/demo/demo03/erp/vo/Demo03StudentErpSaveReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/demo/demo03/inner/Demo03StudentInnerController.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/demo/demo03/inner/Demo03StudentInnerController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/demo/demo03/inner/vo/Demo03StudentInnerPageReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/demo/demo03/inner/vo/Demo03StudentInnerPageReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/demo/demo03/inner/vo/Demo03StudentInnerRespVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/demo/demo03/inner/vo/Demo03StudentInnerRespVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/demo/demo03/inner/vo/Demo03StudentInnerSaveReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/demo/demo03/inner/vo/Demo03StudentInnerSaveReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/demo/demo03/normal/Demo03StudentNormalController.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/demo/demo03/normal/Demo03StudentNormalController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/demo/demo03/normal/vo/Demo03StudentNormalPageReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/demo/demo03/normal/vo/Demo03StudentNormalPageReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/demo/demo03/normal/vo/Demo03StudentNormalRespVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/demo/demo03/normal/vo/Demo03StudentNormalRespVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/demo/demo03/normal/vo/Demo03StudentNormalSaveReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/demo/demo03/normal/vo/Demo03StudentNormalSaveReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/file/FileConfigController.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/file/FileConfigController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/file/FileController.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/file/FileController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/file/vo/config/FileConfigPageReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/file/vo/config/FileConfigPageReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/file/vo/config/FileConfigRespVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/file/vo/config/FileConfigRespVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/file/vo/config/FileConfigSaveReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/file/vo/config/FileConfigSaveReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/file/vo/file/FileCreateReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/file/vo/file/FileCreateReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/file/vo/file/FilePageReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/file/vo/file/FilePageReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/file/vo/file/FilePresignedUrlRespVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/file/vo/file/FilePresignedUrlRespVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/file/vo/file/FileRespVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/file/vo/file/FileRespVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/file/vo/file/FileUploadReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/file/vo/file/FileUploadReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/job/JobController.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/job/JobController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/job/JobLogController.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/job/JobLogController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/job/vo/job/JobPageReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/job/vo/job/JobPageReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/job/vo/job/JobRespVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/job/vo/job/JobRespVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/job/vo/job/JobSaveReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/job/vo/job/JobSaveReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/job/vo/log/JobLogPageReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/job/vo/log/JobLogPageReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/job/vo/log/JobLogRespVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/job/vo/log/JobLogRespVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/logger/ApiAccessLogController.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/logger/ApiAccessLogController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/logger/ApiErrorLogController.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/logger/ApiErrorLogController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/logger/vo/apiaccesslog/ApiAccessLogPageReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/logger/vo/apiaccesslog/ApiAccessLogPageReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/logger/vo/apiaccesslog/ApiAccessLogRespVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/logger/vo/apiaccesslog/ApiAccessLogRespVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/logger/vo/apierrorlog/ApiErrorLogPageReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/logger/vo/apierrorlog/ApiErrorLogPageReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/logger/vo/apierrorlog/ApiErrorLogRespVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/logger/vo/apierrorlog/ApiErrorLogRespVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/redis/RedisController.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/redis/RedisController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/redis/vo/RedisMonitorRespVO$CommandStat$CommandStatBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/redis/vo/RedisMonitorRespVO$CommandStat$CommandStatBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/redis/vo/RedisMonitorRespVO$CommandStat.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/redis/vo/RedisMonitorRespVO$CommandStat.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/redis/vo/RedisMonitorRespVO$RedisMonitorRespVOBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/redis/vo/RedisMonitorRespVO$RedisMonitorRespVOBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/redis/vo/RedisMonitorRespVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/admin/redis/vo/RedisMonitorRespVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/app/file/AppFileController.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/app/file/AppFileController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/app/file/vo/AppFileUploadReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/controller/app/file/vo/AppFileUploadReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/convert/codegen/CodegenConvert.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/convert/codegen/CodegenConvert.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/convert/codegen/CodegenConvertImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/convert/codegen/CodegenConvertImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/convert/config/ConfigConvert.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/convert/config/ConfigConvert.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/convert/config/ConfigConvertImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/convert/config/ConfigConvertImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/convert/file/FileConfigConvert.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/convert/file/FileConfigConvert.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/convert/file/FileConfigConvertImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/convert/file/FileConfigConvertImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/convert/redis/RedisConvert.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/convert/redis/RedisConvert.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/convert/redis/RedisConvertImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/convert/redis/RedisConvertImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/codegen/CodegenColumnDO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/codegen/CodegenColumnDO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/codegen/CodegenTableDO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/codegen/CodegenTableDO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/config/ConfigDO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/config/ConfigDO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/db/DataSourceConfigDO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/db/DataSourceConfigDO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/demo/demo01/Demo01ContactDO$Demo01ContactDOBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/demo/demo01/Demo01ContactDO$Demo01ContactDOBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/demo/demo01/Demo01ContactDO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/demo/demo01/Demo01ContactDO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/demo/demo02/Demo02CategoryDO$Demo02CategoryDOBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/demo/demo02/Demo02CategoryDO$Demo02CategoryDOBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/demo/demo02/Demo02CategoryDO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/demo/demo02/Demo02CategoryDO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/demo/demo03/Demo03CourseDO$Demo03CourseDOBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/demo/demo03/Demo03CourseDO$Demo03CourseDOBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/demo/demo03/Demo03CourseDO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/demo/demo03/Demo03CourseDO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/demo/demo03/Demo03GradeDO$Demo03GradeDOBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/demo/demo03/Demo03GradeDO$Demo03GradeDOBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/demo/demo03/Demo03GradeDO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/demo/demo03/Demo03GradeDO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/demo/demo03/Demo03StudentDO$Demo03StudentDOBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/demo/demo03/Demo03StudentDO$Demo03StudentDOBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/demo/demo03/Demo03StudentDO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/demo/demo03/Demo03StudentDO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/file/FileConfigDO$FileClientConfigTypeHandler$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/file/FileConfigDO$FileClientConfigTypeHandler$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/file/FileConfigDO$FileClientConfigTypeHandler.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/file/FileConfigDO$FileClientConfigTypeHandler.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/file/FileConfigDO$FileConfigDOBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/file/FileConfigDO$FileConfigDOBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/file/FileConfigDO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/file/FileConfigDO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/file/FileContentDO$FileContentDOBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/file/FileContentDO$FileContentDOBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/file/FileContentDO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/file/FileContentDO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/file/FileDO$FileDOBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/file/FileDO$FileDOBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/file/FileDO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/file/FileDO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/job/JobDO$JobDOBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/job/JobDO$JobDOBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/job/JobDO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/job/JobDO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/job/JobLogDO$JobLogDOBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/job/JobLogDO$JobLogDOBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/job/JobLogDO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/job/JobLogDO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/logger/ApiAccessLogDO$ApiAccessLogDOBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/logger/ApiAccessLogDO$ApiAccessLogDOBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/logger/ApiAccessLogDO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/logger/ApiAccessLogDO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/logger/ApiErrorLogDO$ApiErrorLogDOBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/logger/ApiErrorLogDO$ApiErrorLogDOBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/logger/ApiErrorLogDO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/logger/ApiErrorLogDO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/mysql/codegen/CodegenColumnMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/mysql/codegen/CodegenColumnMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/mysql/codegen/CodegenTableMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/mysql/codegen/CodegenTableMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/mysql/config/ConfigMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/mysql/config/ConfigMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/mysql/db/DataSourceConfigMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/mysql/db/DataSourceConfigMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/mysql/demo/demo01/Demo01ContactMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/mysql/demo/demo01/Demo01ContactMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/mysql/demo/demo02/Demo02CategoryMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/mysql/demo/demo02/Demo02CategoryMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/mysql/demo/demo03/erp/Demo03CourseErpMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/mysql/demo/demo03/erp/Demo03CourseErpMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/mysql/demo/demo03/erp/Demo03GradeErpMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/mysql/demo/demo03/erp/Demo03GradeErpMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/mysql/demo/demo03/erp/Demo03StudentErpMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/mysql/demo/demo03/erp/Demo03StudentErpMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/mysql/demo/demo03/inner/Demo03CourseInnerMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/mysql/demo/demo03/inner/Demo03CourseInnerMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/mysql/demo/demo03/inner/Demo03GradeInnerMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/mysql/demo/demo03/inner/Demo03GradeInnerMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/mysql/demo/demo03/inner/Demo03StudentInnerMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/mysql/demo/demo03/inner/Demo03StudentInnerMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/mysql/demo/demo03/normal/Demo03CourseNormalMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/mysql/demo/demo03/normal/Demo03CourseNormalMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/mysql/demo/demo03/normal/Demo03GradeNormalMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/mysql/demo/demo03/normal/Demo03GradeNormalMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/mysql/demo/demo03/normal/Demo03StudentNormalMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/mysql/demo/demo03/normal/Demo03StudentNormalMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/mysql/file/FileConfigMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/mysql/file/FileConfigMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/mysql/file/FileContentMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/mysql/file/FileContentMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/mysql/file/FileMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/mysql/file/FileMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/mysql/job/JobLogMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/mysql/job/JobLogMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/mysql/job/JobMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/mysql/job/JobMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/mysql/logger/ApiAccessLogMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/mysql/logger/ApiAccessLogMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/mysql/logger/ApiErrorLogMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/mysql/logger/ApiErrorLogMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/enums/DictTypeConstants.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/enums/DictTypeConstants.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/enums/ErrorCodeConstants.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/enums/ErrorCodeConstants.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/enums/codegen/CodegenColumnHtmlTypeEnum.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/enums/codegen/CodegenColumnHtmlTypeEnum.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/enums/codegen/CodegenColumnListConditionEnum.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/enums/codegen/CodegenColumnListConditionEnum.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/enums/codegen/CodegenFrontTypeEnum.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/enums/codegen/CodegenFrontTypeEnum.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/enums/codegen/CodegenSceneEnum.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/enums/codegen/CodegenSceneEnum.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/enums/codegen/CodegenTemplateTypeEnum.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/enums/codegen/CodegenTemplateTypeEnum.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/enums/codegen/CodegenVOTypeEnum.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/enums/codegen/CodegenVOTypeEnum.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/enums/config/ConfigTypeEnum.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/enums/config/ConfigTypeEnum.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/enums/job/JobLogStatusEnum.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/enums/job/JobLogStatusEnum.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/enums/job/JobStatusEnum.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/enums/job/JobStatusEnum.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/enums/logger/ApiErrorLogProcessStatusEnum.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/enums/logger/ApiErrorLogProcessStatusEnum.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/framework/codegen/config/CodegenConfiguration.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/framework/codegen/config/CodegenConfiguration.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/framework/codegen/config/CodegenProperties.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/framework/codegen/config/CodegenProperties.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/framework/file/config/YudaoFileAutoConfiguration.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/framework/file/config/YudaoFileAutoConfiguration.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/framework/file/core/client/AbstractFileClient.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/framework/file/core/client/AbstractFileClient.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/framework/file/core/client/FileClient.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/framework/file/core/client/FileClient.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/framework/file/core/client/FileClientConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/framework/file/core/client/FileClientConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/framework/file/core/client/FileClientFactory.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/framework/file/core/client/FileClientFactory.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/framework/file/core/client/FileClientFactoryImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/framework/file/core/client/FileClientFactoryImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/framework/file/core/client/db/DBFileClient.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/framework/file/core/client/db/DBFileClient.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/framework/file/core/client/db/DBFileClientConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/framework/file/core/client/db/DBFileClientConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/framework/file/core/client/ftp/FtpFileClient.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/framework/file/core/client/ftp/FtpFileClient.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/framework/file/core/client/ftp/FtpFileClientConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/framework/file/core/client/ftp/FtpFileClientConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/framework/file/core/client/local/LocalFileClient.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/framework/file/core/client/local/LocalFileClient.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/framework/file/core/client/local/LocalFileClientConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/framework/file/core/client/local/LocalFileClientConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/framework/file/core/client/s3/FilePresignedUrlRespDTO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/framework/file/core/client/s3/FilePresignedUrlRespDTO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/framework/file/core/client/s3/S3FileClient.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/framework/file/core/client/s3/S3FileClient.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/framework/file/core/client/s3/S3FileClientConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/framework/file/core/client/s3/S3FileClientConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/framework/file/core/client/sftp/SftpFileClient.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/framework/file/core/client/sftp/SftpFileClient.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/framework/file/core/client/sftp/SftpFileClientConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/framework/file/core/client/sftp/SftpFileClientConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/framework/file/core/enums/FileStorageEnum.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/framework/file/core/enums/FileStorageEnum.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/framework/file/core/utils/FileTypeUtils.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/framework/file/core/utils/FileTypeUtils.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/framework/monitor/config/AdminServerConfiguration.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/framework/monitor/config/AdminServerConfiguration.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/framework/security/config/SecurityConfiguration$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/framework/security/config/SecurityConfiguration$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/framework/security/config/SecurityConfiguration.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/framework/security/config/SecurityConfiguration.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/framework/web/config/InfraWebConfiguration.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/framework/web/config/InfraWebConfiguration.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/job/job/JobLogCleanJob.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/job/job/JobLogCleanJob.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/job/logger/AccessLogCleanJob.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/job/logger/AccessLogCleanJob.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/job/logger/ErrorLogCleanJob.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/job/logger/ErrorLogCleanJob.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/codegen/CodegenService.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/codegen/CodegenService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/codegen/CodegenServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/codegen/CodegenServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/codegen/inner/CodegenBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/codegen/inner/CodegenBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/codegen/inner/CodegenEngine.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/codegen/inner/CodegenEngine.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/config/ConfigService.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/config/ConfigService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/config/ConfigServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/config/ConfigServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/db/DataSourceConfigService.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/db/DataSourceConfigService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/db/DataSourceConfigServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/db/DataSourceConfigServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/db/DatabaseTableService.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/db/DatabaseTableService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/db/DatabaseTableServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/db/DatabaseTableServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/demo/demo01/Demo01ContactService.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/demo/demo01/Demo01ContactService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/demo/demo01/Demo01ContactServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/demo/demo01/Demo01ContactServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/demo/demo02/Demo02CategoryService.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/demo/demo02/Demo02CategoryService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/demo/demo02/Demo02CategoryServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/demo/demo02/Demo02CategoryServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/demo/demo03/erp/Demo03StudentErpService.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/demo/demo03/erp/Demo03StudentErpService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/demo/demo03/erp/Demo03StudentErpServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/demo/demo03/erp/Demo03StudentErpServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/demo/demo03/inner/Demo03StudentInnerService.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/demo/demo03/inner/Demo03StudentInnerService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/demo/demo03/inner/Demo03StudentInnerServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/demo/demo03/inner/Demo03StudentInnerServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/demo/demo03/normal/Demo03StudentNormalService.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/demo/demo03/normal/Demo03StudentNormalService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/demo/demo03/normal/Demo03StudentNormalServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/demo/demo03/normal/Demo03StudentNormalServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/file/FileConfigService.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/file/FileConfigService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/file/FileConfigServiceImpl$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/file/FileConfigServiceImpl$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/file/FileConfigServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/file/FileConfigServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/file/FileService.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/file/FileService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/file/FileServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/file/FileServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/job/JobLogService.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/job/JobLogService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/job/JobLogServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/job/JobLogServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/job/JobService.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/job/JobService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/job/JobServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/job/JobServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/logger/ApiAccessLogService.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/logger/ApiAccessLogService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/logger/ApiAccessLogServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/logger/ApiAccessLogServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/logger/ApiErrorLogService.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/logger/ApiErrorLogService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/logger/ApiErrorLogServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/logger/ApiErrorLogServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/websocket/DemoWebSocketMessageListener.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/websocket/DemoWebSocketMessageListener.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/websocket/message/DemoReceiveMessage.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/websocket/message/DemoReceiveMessage.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/websocket/message/DemoSendMessage.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/websocket/message/DemoSendMessage.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/generated-sources/annotations/cn/iocoder/yudao/module/infra/convert/codegen/CodegenConvertImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/generated-sources/annotations/cn/iocoder/yudao/module/infra/convert/codegen/CodegenConvertImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/generated-sources/annotations/cn/iocoder/yudao/module/infra/convert/config/ConfigConvertImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/generated-sources/annotations/cn/iocoder/yudao/module/infra/convert/config/ConfigConvertImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/generated-sources/annotations/cn/iocoder/yudao/module/infra/convert/file/FileConfigConvertImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/generated-sources/annotations/cn/iocoder/yudao/module/infra/convert/file/FileConfigConvertImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/generated-sources/annotations/cn/iocoder/yudao/module/infra/convert/redis/RedisConvertImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/generated-sources/annotations/cn/iocoder/yudao/module/infra/convert/redis/RedisConvertImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/src/main/java/cn/iocoder/yudao/module/system/controller/admin/permission/vo/permission/PermissionAssignRoleDataScopeReqVO.java" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/src/main/java/cn/iocoder/yudao/module/system/controller/admin/permission/vo/permission/PermissionAssignRoleDataScopeReqVO.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/src/main/java/cn/iocoder/yudao/module/system/controller/admin/permission/vo/permission/PermissionAssignRoleMenuReqVO.java" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/src/main/java/cn/iocoder/yudao/module/system/controller/admin/permission/vo/permission/PermissionAssignRoleMenuReqVO.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/src/main/java/cn/iocoder/yudao/module/system/controller/admin/permission/vo/permission/PermissionAssignUserRoleReqVO.java" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/src/main/java/cn/iocoder/yudao/module/system/controller/admin/permission/vo/permission/PermissionAssignUserRoleReqVO.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/dept/DeptApi.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/dept/DeptApi.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/dept/DeptApiImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/dept/DeptApiImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/dept/PostApi.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/dept/PostApi.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/dept/PostApiImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/dept/PostApiImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/dept/dto/DeptRespDTO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/dept/dto/DeptRespDTO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/dept/dto/PostRespDTO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/dept/dto/PostRespDTO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/dict/DictDataApi.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/dict/DictDataApi.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/dict/DictDataApiImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/dict/DictDataApiImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/logger/LoginLogApi.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/logger/LoginLogApi.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/logger/LoginLogApiImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/logger/LoginLogApiImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/logger/OperateLogApi.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/logger/OperateLogApi.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/logger/OperateLogApiImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/logger/OperateLogApiImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/logger/dto/LoginLogCreateReqDTO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/logger/dto/LoginLogCreateReqDTO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/logger/dto/OperateLogPageReqDTO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/logger/dto/OperateLogPageReqDTO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/logger/dto/OperateLogRespDTO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/logger/dto/OperateLogRespDTO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/mail/MailSendApi.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/mail/MailSendApi.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/mail/MailSendApiImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/mail/MailSendApiImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/mail/dto/MailSendSingleToUserReqDTO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/mail/dto/MailSendSingleToUserReqDTO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/notify/NotifyMessageSendApi.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/notify/NotifyMessageSendApi.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/notify/NotifyMessageSendApiImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/notify/NotifyMessageSendApiImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/notify/dto/NotifySendSingleToUserReqDTO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/notify/dto/NotifySendSingleToUserReqDTO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/notify/dto/NotifyTemplateReqDTO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/notify/dto/NotifyTemplateReqDTO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/oauth2/OAuth2TokenApiImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/oauth2/OAuth2TokenApiImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/permission/PermissionApi.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/permission/PermissionApi.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/permission/PermissionApiImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/permission/PermissionApiImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/permission/RoleApi.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/permission/RoleApi.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/permission/RoleApiImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/permission/RoleApiImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/sms/SmsCodeApi.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/sms/SmsCodeApi.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/sms/SmsCodeApiImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/sms/SmsCodeApiImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/sms/SmsSendApi.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/sms/SmsSendApi.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/sms/SmsSendApiImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/sms/SmsSendApiImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/sms/dto/code/SmsCodeSendReqDTO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/sms/dto/code/SmsCodeSendReqDTO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/sms/dto/code/SmsCodeUseReqDTO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/sms/dto/code/SmsCodeUseReqDTO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/sms/dto/code/SmsCodeValidateReqDTO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/sms/dto/code/SmsCodeValidateReqDTO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/sms/dto/send/SmsSendSingleToUserReqDTO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/sms/dto/send/SmsSendSingleToUserReqDTO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/social/SocialClientApi.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/social/SocialClientApi.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/social/SocialClientApiImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/social/SocialClientApiImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/social/SocialUserApi.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/social/SocialUserApi.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/social/SocialUserApiImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/social/SocialUserApiImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/social/dto/SocialUserBindReqDTO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/social/dto/SocialUserBindReqDTO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/social/dto/SocialUserRespDTO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/social/dto/SocialUserRespDTO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/social/dto/SocialUserUnbindReqDTO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/social/dto/SocialUserUnbindReqDTO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/social/dto/SocialWxJsapiSignatureRespDTO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/social/dto/SocialWxJsapiSignatureRespDTO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/social/dto/SocialWxPhoneNumberInfoRespDTO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/social/dto/SocialWxPhoneNumberInfoRespDTO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/social/dto/SocialWxQrcodeReqDTO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/social/dto/SocialWxQrcodeReqDTO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/social/dto/SocialWxaOrderNotifyConfirmReceiveReqDTO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/social/dto/SocialWxaOrderNotifyConfirmReceiveReqDTO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/social/dto/SocialWxaOrderUploadShippingInfoReqDTO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/social/dto/SocialWxaOrderUploadShippingInfoReqDTO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/social/dto/SocialWxaSubscribeMessageSendReqDTO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/social/dto/SocialWxaSubscribeMessageSendReqDTO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/social/dto/SocialWxaSubscribeTemplateRespDTO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/social/dto/SocialWxaSubscribeTemplateRespDTO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/tenant/TenantApiImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/tenant/TenantApiImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/user/AdminUserApi.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/user/AdminUserApi.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/user/AdminUserApiImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/user/AdminUserApiImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/user/dto/AdminUserRespDTO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/api/user/dto/AdminUserRespDTO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/auth/AuthController.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/auth/AuthController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/auth/vo/AuthLoginReqVO$AuthLoginReqVOBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/auth/vo/AuthLoginReqVO$AuthLoginReqVOBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/auth/vo/AuthLoginReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/auth/vo/AuthLoginReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/auth/vo/AuthLoginRespVO$AuthLoginRespVOBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/auth/vo/AuthLoginRespVO$AuthLoginRespVOBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/auth/vo/AuthLoginRespVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/auth/vo/AuthLoginRespVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/auth/vo/AuthMenuRespVO$AuthMenuRespVOBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/auth/vo/AuthMenuRespVO$AuthMenuRespVOBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/auth/vo/AuthMenuRespVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/auth/vo/AuthMenuRespVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/auth/vo/AuthPermissionInfoRespVO$AuthPermissionInfoRespVOBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/auth/vo/AuthPermissionInfoRespVO$AuthPermissionInfoRespVOBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/auth/vo/AuthPermissionInfoRespVO$MenuVO$MenuVOBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/auth/vo/AuthPermissionInfoRespVO$MenuVO$MenuVOBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/auth/vo/AuthPermissionInfoRespVO$MenuVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/auth/vo/AuthPermissionInfoRespVO$MenuVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/auth/vo/AuthPermissionInfoRespVO$UserVO$UserVOBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/auth/vo/AuthPermissionInfoRespVO$UserVO$UserVOBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/auth/vo/AuthPermissionInfoRespVO$UserVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/auth/vo/AuthPermissionInfoRespVO$UserVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/auth/vo/AuthPermissionInfoRespVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/auth/vo/AuthPermissionInfoRespVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/auth/vo/AuthRegisterReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/auth/vo/AuthRegisterReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/auth/vo/AuthResetPasswordReqVO$AuthResetPasswordReqVOBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/auth/vo/AuthResetPasswordReqVO$AuthResetPasswordReqVOBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/auth/vo/AuthResetPasswordReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/auth/vo/AuthResetPasswordReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/auth/vo/AuthSmsLoginReqVO$AuthSmsLoginReqVOBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/auth/vo/AuthSmsLoginReqVO$AuthSmsLoginReqVOBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/auth/vo/AuthSmsLoginReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/auth/vo/AuthSmsLoginReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/auth/vo/AuthSmsSendReqVO$AuthSmsSendReqVOBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/auth/vo/AuthSmsSendReqVO$AuthSmsSendReqVOBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/auth/vo/AuthSmsSendReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/auth/vo/AuthSmsSendReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/auth/vo/AuthSocialLoginReqVO$AuthSocialLoginReqVOBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/auth/vo/AuthSocialLoginReqVO$AuthSocialLoginReqVOBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/auth/vo/AuthSocialLoginReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/auth/vo/AuthSocialLoginReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/auth/vo/CaptchaVerificationReqVO$CodeEnableGroup.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/auth/vo/CaptchaVerificationReqVO$CodeEnableGroup.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/auth/vo/CaptchaVerificationReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/auth/vo/CaptchaVerificationReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/captcha/CaptchaController.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/captcha/CaptchaController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/dept/DeptController.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/dept/DeptController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/dept/PostController.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/dept/PostController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/dept/vo/dept/DeptListReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/dept/vo/dept/DeptListReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/dept/vo/dept/DeptRespVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/dept/vo/dept/DeptRespVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/dept/vo/dept/DeptSaveReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/dept/vo/dept/DeptSaveReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/dept/vo/dept/DeptSimpleRespVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/dept/vo/dept/DeptSimpleRespVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/dept/vo/post/PostPageReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/dept/vo/post/PostPageReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/dept/vo/post/PostRespVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/dept/vo/post/PostRespVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/dept/vo/post/PostSaveReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/dept/vo/post/PostSaveReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/dept/vo/post/PostSimpleRespVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/dept/vo/post/PostSimpleRespVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/dict/DictDataController.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/dict/DictDataController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/dict/DictTypeController.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/dict/DictTypeController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/dict/vo/data/DictDataPageReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/dict/vo/data/DictDataPageReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/dict/vo/data/DictDataRespVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/dict/vo/data/DictDataRespVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/dict/vo/data/DictDataSaveReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/dict/vo/data/DictDataSaveReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/dict/vo/data/DictDataSimpleRespVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/dict/vo/data/DictDataSimpleRespVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/dict/vo/type/DictTypePageReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/dict/vo/type/DictTypePageReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/dict/vo/type/DictTypeRespVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/dict/vo/type/DictTypeRespVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/dict/vo/type/DictTypeSaveReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/dict/vo/type/DictTypeSaveReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/dict/vo/type/DictTypeSimpleRespVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/dict/vo/type/DictTypeSimpleRespVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/ip/AreaController.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/ip/AreaController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/ip/vo/AreaNodeRespVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/ip/vo/AreaNodeRespVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/logger/LoginLogController.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/logger/LoginLogController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/logger/OperateLogController.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/logger/OperateLogController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/logger/vo/loginlog/LoginLogPageReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/logger/vo/loginlog/LoginLogPageReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/logger/vo/loginlog/LoginLogRespVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/logger/vo/loginlog/LoginLogRespVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/logger/vo/operatelog/OperateLogPageReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/logger/vo/operatelog/OperateLogPageReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/logger/vo/operatelog/OperateLogRespVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/logger/vo/operatelog/OperateLogRespVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/mail/MailAccountController.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/mail/MailAccountController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/mail/MailLogController.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/mail/MailLogController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/mail/MailTemplateController.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/mail/MailTemplateController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/mail/vo/account/MailAccountPageReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/mail/vo/account/MailAccountPageReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/mail/vo/account/MailAccountRespVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/mail/vo/account/MailAccountRespVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/mail/vo/account/MailAccountSaveReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/mail/vo/account/MailAccountSaveReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/mail/vo/account/MailAccountSimpleRespVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/mail/vo/account/MailAccountSimpleRespVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/mail/vo/log/MailLogPageReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/mail/vo/log/MailLogPageReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/mail/vo/log/MailLogRespVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/mail/vo/log/MailLogRespVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/mail/vo/template/MailTemplatePageReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/mail/vo/template/MailTemplatePageReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/mail/vo/template/MailTemplateRespVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/mail/vo/template/MailTemplateRespVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/mail/vo/template/MailTemplateSaveReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/mail/vo/template/MailTemplateSaveReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/mail/vo/template/MailTemplateSendReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/mail/vo/template/MailTemplateSendReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/mail/vo/template/MailTemplateSimpleRespVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/mail/vo/template/MailTemplateSimpleRespVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/notice/NoticeController.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/notice/NoticeController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/notice/vo/NoticePageReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/notice/vo/NoticePageReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/notice/vo/NoticeRespVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/notice/vo/NoticeRespVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/notice/vo/NoticeSaveReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/notice/vo/NoticeSaveReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/notify/NotifyMessageController.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/notify/NotifyMessageController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/notify/NotifyTemplateController.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/notify/NotifyTemplateController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/notify/vo/message/NotifyMessageMyPageReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/notify/vo/message/NotifyMessageMyPageReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/notify/vo/message/NotifyMessagePageReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/notify/vo/message/NotifyMessagePageReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/notify/vo/message/NotifyMessageRespVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/notify/vo/message/NotifyMessageRespVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/notify/vo/template/NotifyTemplatePageReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/notify/vo/template/NotifyTemplatePageReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/notify/vo/template/NotifyTemplateRespVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/notify/vo/template/NotifyTemplateRespVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/notify/vo/template/NotifyTemplateSaveReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/notify/vo/template/NotifyTemplateSaveReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/notify/vo/template/NotifyTemplateSendReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/notify/vo/template/NotifyTemplateSendReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/oauth2/OAuth2ClientController.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/oauth2/OAuth2ClientController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/oauth2/OAuth2OpenController$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/oauth2/OAuth2OpenController.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/oauth2/OAuth2OpenController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/oauth2/OAuth2TokenController.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/oauth2/OAuth2TokenController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/oauth2/OAuth2UserController.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/oauth2/OAuth2UserController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/oauth2/vo/client/OAuth2ClientPageReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/oauth2/vo/client/OAuth2ClientPageReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/oauth2/vo/client/OAuth2ClientRespVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/oauth2/vo/client/OAuth2ClientRespVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/oauth2/vo/client/OAuth2ClientSaveReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/oauth2/vo/client/OAuth2ClientSaveReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/oauth2/vo/open/OAuth2OpenAccessTokenRespVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/oauth2/vo/open/OAuth2OpenAccessTokenRespVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/oauth2/vo/open/OAuth2OpenAuthorizeInfoRespVO$Client.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/oauth2/vo/open/OAuth2OpenAuthorizeInfoRespVO$Client.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/oauth2/vo/open/OAuth2OpenAuthorizeInfoRespVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/oauth2/vo/open/OAuth2OpenAuthorizeInfoRespVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/oauth2/vo/open/OAuth2OpenCheckTokenRespVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/oauth2/vo/open/OAuth2OpenCheckTokenRespVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/oauth2/vo/token/OAuth2AccessTokenPageReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/oauth2/vo/token/OAuth2AccessTokenPageReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/oauth2/vo/token/OAuth2AccessTokenRespVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/oauth2/vo/token/OAuth2AccessTokenRespVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/oauth2/vo/user/OAuth2UserInfoRespVO$Dept.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/oauth2/vo/user/OAuth2UserInfoRespVO$Dept.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/oauth2/vo/user/OAuth2UserInfoRespVO$Post.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/oauth2/vo/user/OAuth2UserInfoRespVO$Post.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/oauth2/vo/user/OAuth2UserInfoRespVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/oauth2/vo/user/OAuth2UserInfoRespVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/oauth2/vo/user/OAuth2UserUpdateReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/oauth2/vo/user/OAuth2UserUpdateReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/permission/MenuController.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/permission/MenuController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/permission/PermissionController.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/permission/PermissionController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/permission/RoleController.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/permission/RoleController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/permission/vo/menu/MenuListReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/permission/vo/menu/MenuListReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/permission/vo/menu/MenuRespVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/permission/vo/menu/MenuRespVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/permission/vo/menu/MenuSaveVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/permission/vo/menu/MenuSaveVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/permission/vo/menu/MenuSimpleRespVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/permission/vo/menu/MenuSimpleRespVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/permission/vo/permission/PermissionAssignRoleDataScopeReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/permission/vo/permission/PermissionAssignRoleDataScopeReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/permission/vo/permission/PermissionAssignRoleMenuReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/permission/vo/permission/PermissionAssignRoleMenuReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/permission/vo/permission/PermissionAssignUserRoleReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/permission/vo/permission/PermissionAssignUserRoleReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/permission/vo/role/RolePageReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/permission/vo/role/RolePageReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/permission/vo/role/RoleRespVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/permission/vo/role/RoleRespVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/permission/vo/role/RoleSaveReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/permission/vo/role/RoleSaveReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/permission/vo/role/RoleSimpleRespVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/permission/vo/role/RoleSimpleRespVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/sms/SmsCallbackController.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/sms/SmsCallbackController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/sms/SmsChannelController.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/sms/SmsChannelController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/sms/SmsLogController.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/sms/SmsLogController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/sms/SmsTemplateController.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/sms/SmsTemplateController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/sms/vo/channel/SmsChannelPageReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/sms/vo/channel/SmsChannelPageReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/sms/vo/channel/SmsChannelRespVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/sms/vo/channel/SmsChannelRespVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/sms/vo/channel/SmsChannelSaveReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/sms/vo/channel/SmsChannelSaveReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/sms/vo/channel/SmsChannelSimpleRespVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/sms/vo/channel/SmsChannelSimpleRespVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/sms/vo/log/SmsLogPageReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/sms/vo/log/SmsLogPageReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/sms/vo/log/SmsLogRespVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/sms/vo/log/SmsLogRespVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/sms/vo/template/SmsTemplatePageReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/sms/vo/template/SmsTemplatePageReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/sms/vo/template/SmsTemplateRespVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/sms/vo/template/SmsTemplateRespVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/sms/vo/template/SmsTemplateSaveReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/sms/vo/template/SmsTemplateSaveReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/sms/vo/template/SmsTemplateSendReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/sms/vo/template/SmsTemplateSendReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/socail/SocialClientController.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/socail/SocialClientController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/socail/SocialUserController.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/socail/SocialUserController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/socail/vo/client/SocialClientPageReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/socail/vo/client/SocialClientPageReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/socail/vo/client/SocialClientRespVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/socail/vo/client/SocialClientRespVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/socail/vo/client/SocialClientSaveReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/socail/vo/client/SocialClientSaveReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/socail/vo/user/SocialUserBindReqVO$SocialUserBindReqVOBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/socail/vo/user/SocialUserBindReqVO$SocialUserBindReqVOBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/socail/vo/user/SocialUserBindReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/socail/vo/user/SocialUserBindReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/socail/vo/user/SocialUserPageReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/socail/vo/user/SocialUserPageReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/socail/vo/user/SocialUserRespVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/socail/vo/user/SocialUserRespVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/socail/vo/user/SocialUserUnbindReqVO$SocialUserUnbindReqVOBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/socail/vo/user/SocialUserUnbindReqVO$SocialUserUnbindReqVOBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/socail/vo/user/SocialUserUnbindReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/socail/vo/user/SocialUserUnbindReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/tenant/TenantController.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/tenant/TenantController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/tenant/TenantPackageController.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/tenant/TenantPackageController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/tenant/vo/packages/TenantPackagePageReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/tenant/vo/packages/TenantPackagePageReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/tenant/vo/packages/TenantPackageRespVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/tenant/vo/packages/TenantPackageRespVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/tenant/vo/packages/TenantPackageSaveReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/tenant/vo/packages/TenantPackageSaveReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/tenant/vo/packages/TenantPackageSimpleRespVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/tenant/vo/packages/TenantPackageSimpleRespVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/tenant/vo/tenant/TenantPageReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/tenant/vo/tenant/TenantPageReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/tenant/vo/tenant/TenantRespVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/tenant/vo/tenant/TenantRespVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/tenant/vo/tenant/TenantSaveReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/tenant/vo/tenant/TenantSaveReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/user/UserController.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/user/UserController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/user/UserProfileController.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/user/UserProfileController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/user/vo/profile/UserProfileRespVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/user/vo/profile/UserProfileRespVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/user/vo/profile/UserProfileUpdatePasswordReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/user/vo/profile/UserProfileUpdatePasswordReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/user/vo/profile/UserProfileUpdateReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/user/vo/profile/UserProfileUpdateReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/user/vo/user/UserImportExcelVO$UserImportExcelVOBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/user/vo/user/UserImportExcelVO$UserImportExcelVOBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/user/vo/user/UserImportExcelVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/user/vo/user/UserImportExcelVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/user/vo/user/UserImportRespVO$UserImportRespVOBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/user/vo/user/UserImportRespVO$UserImportRespVOBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/user/vo/user/UserImportRespVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/user/vo/user/UserImportRespVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/user/vo/user/UserPageReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/user/vo/user/UserPageReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/user/vo/user/UserRespVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/user/vo/user/UserRespVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/user/vo/user/UserSaveReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/user/vo/user/UserSaveReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/user/vo/user/UserSimpleRespVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/user/vo/user/UserSimpleRespVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/user/vo/user/UserUpdatePasswordReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/user/vo/user/UserUpdatePasswordReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/user/vo/user/UserUpdateStatusReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/user/vo/user/UserUpdateStatusReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/app/dict/AppDictDataController.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/app/dict/AppDictDataController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/app/dict/vo/AppDictDataRespVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/app/dict/vo/AppDictDataRespVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/app/ip/AppAreaController.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/app/ip/AppAreaController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/app/ip/vo/AppAreaNodeRespVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/app/ip/vo/AppAreaNodeRespVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/convert/auth/AuthConvert.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/convert/auth/AuthConvert.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/convert/auth/AuthConvertImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/convert/auth/AuthConvertImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/convert/oauth2/OAuth2OpenConvert.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/convert/oauth2/OAuth2OpenConvert.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/convert/oauth2/OAuth2OpenConvertImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/convert/oauth2/OAuth2OpenConvertImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/convert/tenant/TenantConvert.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/convert/tenant/TenantConvert.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/convert/tenant/TenantConvertImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/convert/tenant/TenantConvertImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/convert/user/UserConvert.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/convert/user/UserConvert.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/convert/user/UserConvertImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/convert/user/UserConvertImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/dept/DeptDO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/dept/DeptDO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/dept/PostDO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/dept/PostDO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/dept/UserPostDO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/dept/UserPostDO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/dict/DictDataDO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/dict/DictDataDO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/dict/DictTypeDO$DictTypeDOBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/dict/DictTypeDO$DictTypeDOBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/dict/DictTypeDO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/dict/DictTypeDO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/logger/LoginLogDO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/logger/LoginLogDO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/logger/OperateLogDO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/logger/OperateLogDO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/mail/MailAccountDO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/mail/MailAccountDO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/mail/MailLogDO$MailLogDOBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/mail/MailLogDO$MailLogDOBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/mail/MailLogDO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/mail/MailLogDO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/mail/MailTemplateDO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/mail/MailTemplateDO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/notice/NoticeDO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/notice/NoticeDO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/notify/NotifyMessageDO$NotifyMessageDOBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/notify/NotifyMessageDO$NotifyMessageDOBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/notify/NotifyMessageDO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/notify/NotifyMessageDO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/notify/NotifyTemplateDO$NotifyTemplateDOBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/notify/NotifyTemplateDO$NotifyTemplateDOBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/notify/NotifyTemplateDO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/notify/NotifyTemplateDO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/oauth2/OAuth2AccessTokenDO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/oauth2/OAuth2AccessTokenDO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/oauth2/OAuth2ApproveDO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/oauth2/OAuth2ApproveDO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/oauth2/OAuth2ClientDO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/oauth2/OAuth2ClientDO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/oauth2/OAuth2CodeDO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/oauth2/OAuth2CodeDO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/oauth2/OAuth2RefreshTokenDO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/oauth2/OAuth2RefreshTokenDO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/permission/MenuDO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/permission/MenuDO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/permission/RoleDO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/permission/RoleDO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/permission/RoleMenuDO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/permission/RoleMenuDO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/permission/UserRoleDO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/permission/UserRoleDO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/sms/SmsChannelDO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/sms/SmsChannelDO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/sms/SmsCodeDO$SmsCodeDOBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/sms/SmsCodeDO$SmsCodeDOBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/sms/SmsCodeDO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/sms/SmsCodeDO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/sms/SmsLogDO$SmsLogDOBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/sms/SmsLogDO$SmsLogDOBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/sms/SmsLogDO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/sms/SmsLogDO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/sms/SmsTemplateDO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/sms/SmsTemplateDO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/social/SocialClientDO$SocialClientDOBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/social/SocialClientDO$SocialClientDOBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/social/SocialClientDO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/social/SocialClientDO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/social/SocialUserBindDO$SocialUserBindDOBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/social/SocialUserBindDO$SocialUserBindDOBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/social/SocialUserBindDO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/social/SocialUserBindDO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/social/SocialUserDO$SocialUserDOBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/social/SocialUserDO$SocialUserDOBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/social/SocialUserDO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/social/SocialUserDO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/tenant/TenantDO$TenantDOBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/tenant/TenantDO$TenantDOBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/tenant/TenantDO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/tenant/TenantDO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/tenant/TenantPackageDO$TenantPackageDOBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/tenant/TenantPackageDO$TenantPackageDOBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/tenant/TenantPackageDO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/tenant/TenantPackageDO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/user/AdminUserDO$AdminUserDOBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/user/AdminUserDO$AdminUserDOBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/user/AdminUserDO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/dataobject/user/AdminUserDO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/mysql/dept/DeptMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/mysql/dept/DeptMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/mysql/dept/PostMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/mysql/dept/PostMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/mysql/dept/UserPostMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/mysql/dept/UserPostMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/mysql/dict/DictDataMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/mysql/dict/DictDataMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/mysql/dict/DictTypeMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/mysql/dict/DictTypeMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/mysql/logger/LoginLogMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/mysql/logger/LoginLogMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/mysql/logger/OperateLogMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/mysql/logger/OperateLogMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/mysql/mail/MailAccountMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/mysql/mail/MailAccountMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/mysql/mail/MailLogMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/mysql/mail/MailLogMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/mysql/mail/MailTemplateMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/mysql/mail/MailTemplateMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/mysql/notice/NoticeMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/mysql/notice/NoticeMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/mysql/notify/NotifyMessageMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/mysql/notify/NotifyMessageMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/mysql/notify/NotifyTemplateMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/mysql/notify/NotifyTemplateMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/mysql/oauth2/OAuth2AccessTokenMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/mysql/oauth2/OAuth2AccessTokenMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/mysql/oauth2/OAuth2ApproveMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/mysql/oauth2/OAuth2ApproveMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/mysql/oauth2/OAuth2ClientMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/mysql/oauth2/OAuth2ClientMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/mysql/oauth2/OAuth2CodeMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/mysql/oauth2/OAuth2CodeMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/mysql/oauth2/OAuth2RefreshTokenMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/mysql/oauth2/OAuth2RefreshTokenMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/mysql/permission/MenuMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/mysql/permission/MenuMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/mysql/permission/RoleMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/mysql/permission/RoleMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/mysql/permission/RoleMenuMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/mysql/permission/RoleMenuMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/mysql/permission/UserRoleMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/mysql/permission/UserRoleMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/mysql/sms/SmsChannelMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/mysql/sms/SmsChannelMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/mysql/sms/SmsCodeMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/mysql/sms/SmsCodeMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/mysql/sms/SmsLogMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/mysql/sms/SmsLogMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/mysql/sms/SmsTemplateMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/mysql/sms/SmsTemplateMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/mysql/social/SocialClientMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/mysql/social/SocialClientMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/mysql/social/SocialUserBindMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/mysql/social/SocialUserBindMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/mysql/social/SocialUserMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/mysql/social/SocialUserMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/mysql/tenant/TenantMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/mysql/tenant/TenantMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/mysql/tenant/TenantPackageMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/mysql/tenant/TenantPackageMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/mysql/user/AdminUserMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/mysql/user/AdminUserMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/redis/RedisKeyConstants.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/redis/RedisKeyConstants.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/redis/oauth2/OAuth2AccessTokenRedisDAO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/dal/redis/oauth2/OAuth2AccessTokenRedisDAO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/enums/DictTypeConstants.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/enums/DictTypeConstants.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/enums/ErrorCodeConstants.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/enums/ErrorCodeConstants.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/enums/LogRecordConstants.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/enums/LogRecordConstants.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/enums/common/SexEnum.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/enums/common/SexEnum.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/enums/logger/LoginLogTypeEnum.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/enums/logger/LoginLogTypeEnum.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/enums/logger/LoginResultEnum.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/enums/logger/LoginResultEnum.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/enums/mail/MailSendStatusEnum.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/enums/mail/MailSendStatusEnum.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/enums/notice/NoticeTypeEnum.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/enums/notice/NoticeTypeEnum.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/enums/notify/NotifyTemplateTypeEnum.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/enums/notify/NotifyTemplateTypeEnum.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/enums/oauth2/OAuth2ClientConstants.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/enums/oauth2/OAuth2ClientConstants.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/enums/oauth2/OAuth2GrantTypeEnum.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/enums/oauth2/OAuth2GrantTypeEnum.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/enums/permission/DataScopeEnum.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/enums/permission/DataScopeEnum.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/enums/permission/MenuTypeEnum.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/enums/permission/MenuTypeEnum.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/enums/permission/RoleCodeEnum.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/enums/permission/RoleCodeEnum.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/enums/permission/RoleTypeEnum.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/enums/permission/RoleTypeEnum.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/enums/sms/SmsReceiveStatusEnum.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/enums/sms/SmsReceiveStatusEnum.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/enums/sms/SmsSceneEnum.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/enums/sms/SmsSceneEnum.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/enums/sms/SmsSendStatusEnum.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/enums/sms/SmsSendStatusEnum.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/enums/sms/SmsTemplateTypeEnum.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/enums/sms/SmsTemplateTypeEnum.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/enums/social/SocialTypeEnum.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/enums/social/SocialTypeEnum.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/framework/captcha/config/YudaoCaptchaConfiguration.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/framework/captcha/config/YudaoCaptchaConfiguration.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/framework/captcha/core/RedisCaptchaServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/framework/captcha/core/RedisCaptchaServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/framework/datapermission/config/DataPermissionConfiguration.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/framework/datapermission/config/DataPermissionConfiguration.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/framework/justauth/config/YudaoJustAuthConfiguration.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/framework/justauth/config/YudaoJustAuthConfiguration.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/framework/justauth/core/AuthRequestFactory$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/framework/justauth/core/AuthRequestFactory.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/framework/justauth/core/AuthRequestFactory.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/framework/operatelog/core/AdminUserParseFunction.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/framework/operatelog/core/AdminUserParseFunction.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/framework/operatelog/core/AreaParseFunction.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/framework/operatelog/core/AreaParseFunction.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/framework/operatelog/core/BooleanParseFunction.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/framework/operatelog/core/BooleanParseFunction.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/framework/operatelog/core/DeptParseFunction.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/framework/operatelog/core/DeptParseFunction.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/framework/operatelog/core/PostParseFunction.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/framework/operatelog/core/PostParseFunction.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/framework/operatelog/core/SexParseFunction.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/framework/operatelog/core/SexParseFunction.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/framework/sms/config/SmsCodeProperties.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/framework/sms/config/SmsCodeProperties.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/framework/sms/config/SmsConfiguration.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/framework/sms/config/SmsConfiguration.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/framework/sms/core/client/SmsClient.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/framework/sms/core/client/SmsClient.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/framework/sms/core/client/SmsClientFactory.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/framework/sms/core/client/SmsClientFactory.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/framework/sms/core/client/dto/SmsReceiveRespDTO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/framework/sms/core/client/dto/SmsReceiveRespDTO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/framework/sms/core/client/dto/SmsSendRespDTO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/framework/sms/core/client/dto/SmsSendRespDTO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/framework/sms/core/client/dto/SmsTemplateRespDTO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/framework/sms/core/client/dto/SmsTemplateRespDTO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/framework/sms/core/client/impl/AbstractSmsClient.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/framework/sms/core/client/impl/AbstractSmsClient.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/framework/sms/core/client/impl/AliyunSmsClient.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/framework/sms/core/client/impl/AliyunSmsClient.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/framework/sms/core/client/impl/DebugDingTalkSmsClient.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/framework/sms/core/client/impl/DebugDingTalkSmsClient.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/framework/sms/core/client/impl/HuaweiSmsClient.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/framework/sms/core/client/impl/HuaweiSmsClient.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/framework/sms/core/client/impl/QiniuSmsClient$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/framework/sms/core/client/impl/QiniuSmsClient$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/framework/sms/core/client/impl/QiniuSmsClient.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/framework/sms/core/client/impl/QiniuSmsClient.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/framework/sms/core/client/impl/SmsClientFactoryImpl$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/framework/sms/core/client/impl/SmsClientFactoryImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/framework/sms/core/client/impl/SmsClientFactoryImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/framework/sms/core/client/impl/TencentSmsClient.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/framework/sms/core/client/impl/TencentSmsClient.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/framework/sms/core/enums/SmsChannelEnum.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/framework/sms/core/enums/SmsChannelEnum.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/framework/sms/core/enums/SmsTemplateAuditStatusEnum.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/framework/sms/core/enums/SmsTemplateAuditStatusEnum.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/framework/sms/core/property/SmsChannelProperties.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/framework/sms/core/property/SmsChannelProperties.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/framework/web/config/SystemWebConfiguration.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/framework/web/config/SystemWebConfiguration.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/job/DemoJob.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/job/DemoJob.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/mq/consumer/mail/MailSendConsumer.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/mq/consumer/mail/MailSendConsumer.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/mq/consumer/sms/SmsSendConsumer.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/mq/consumer/sms/SmsSendConsumer.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/mq/message/mail/MailSendMessage.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/mq/message/mail/MailSendMessage.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/mq/message/sms/SmsSendMessage.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/mq/message/sms/SmsSendMessage.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/mq/producer/mail/MailProducer.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/mq/producer/mail/MailProducer.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/mq/producer/sms/SmsProducer.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/mq/producer/sms/SmsProducer.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/auth/AdminAuthService.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/auth/AdminAuthService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/auth/AdminAuthServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/auth/AdminAuthServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/dept/DeptService.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/dept/DeptService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/dept/DeptServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/dept/DeptServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/dept/PostService.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/dept/PostService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/dept/PostServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/dept/PostServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/dict/DictDataService.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/dict/DictDataService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/dict/DictDataServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/dict/DictDataServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/dict/DictTypeService.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/dict/DictTypeService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/dict/DictTypeServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/dict/DictTypeServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/logger/LoginLogService.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/logger/LoginLogService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/logger/LoginLogServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/logger/LoginLogServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/logger/OperateLogService.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/logger/OperateLogService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/logger/OperateLogServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/logger/OperateLogServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/mail/MailAccountService.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/mail/MailAccountService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/mail/MailAccountServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/mail/MailAccountServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/mail/MailLogService.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/mail/MailLogService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/mail/MailLogServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/mail/MailLogServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/mail/MailSendService.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/mail/MailSendService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/mail/MailSendServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/mail/MailSendServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/mail/MailTemplateService.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/mail/MailTemplateService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/mail/MailTemplateServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/mail/MailTemplateServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/member/MemberService.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/member/MemberService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/member/MemberServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/member/MemberServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/notice/NoticeService.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/notice/NoticeService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/notice/NoticeServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/notice/NoticeServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/notify/NotifyMessageService.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/notify/NotifyMessageService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/notify/NotifyMessageServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/notify/NotifyMessageServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/notify/NotifySendService.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/notify/NotifySendService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/notify/NotifySendServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/notify/NotifySendServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/notify/NotifyTemplateService.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/notify/NotifyTemplateService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/notify/NotifyTemplateServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/notify/NotifyTemplateServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/oauth2/OAuth2ApproveService.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/oauth2/OAuth2ApproveService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/oauth2/OAuth2ApproveServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/oauth2/OAuth2ApproveServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/oauth2/OAuth2ClientService.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/oauth2/OAuth2ClientService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/oauth2/OAuth2ClientServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/oauth2/OAuth2ClientServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/oauth2/OAuth2CodeService.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/oauth2/OAuth2CodeService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/oauth2/OAuth2CodeServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/oauth2/OAuth2CodeServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/oauth2/OAuth2GrantService.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/oauth2/OAuth2GrantService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/oauth2/OAuth2GrantServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/oauth2/OAuth2GrantServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/oauth2/OAuth2TokenService.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/oauth2/OAuth2TokenService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/oauth2/OAuth2TokenServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/oauth2/OAuth2TokenServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/permission/MenuService.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/permission/MenuService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/permission/MenuServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/permission/MenuServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/permission/PermissionService.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/permission/PermissionService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/permission/PermissionServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/permission/PermissionServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/permission/RoleService.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/permission/RoleService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/permission/RoleServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/permission/RoleServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/sms/SmsChannelService.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/sms/SmsChannelService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/sms/SmsChannelServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/sms/SmsChannelServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/sms/SmsCodeService.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/sms/SmsCodeService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/sms/SmsCodeServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/sms/SmsCodeServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/sms/SmsLogService.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/sms/SmsLogService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/sms/SmsLogServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/sms/SmsLogServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/sms/SmsSendService.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/sms/SmsSendService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/sms/SmsSendServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/sms/SmsSendServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/sms/SmsTemplateService.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/sms/SmsTemplateService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/sms/SmsTemplateServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/sms/SmsTemplateServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/social/SocialClientService.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/social/SocialClientService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/social/SocialClientServiceImpl$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/social/SocialClientServiceImpl$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/social/SocialClientServiceImpl$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/social/SocialClientServiceImpl$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/social/SocialClientServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/social/SocialClientServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/social/SocialUserService.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/social/SocialUserService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/social/SocialUserServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/social/SocialUserServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/tenant/TenantPackageService.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/tenant/TenantPackageService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/tenant/TenantPackageServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/tenant/TenantPackageServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/tenant/TenantService.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/tenant/TenantService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/tenant/TenantServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/tenant/TenantServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/tenant/handler/TenantInfoHandler.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/tenant/handler/TenantInfoHandler.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/tenant/handler/TenantMenuHandler.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/tenant/handler/TenantMenuHandler.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/user/AdminUserService.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/user/AdminUserService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/user/AdminUserServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/service/user/AdminUserServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/util/oauth2/OAuth2Utils.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/util/oauth2/OAuth2Utils.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/generated-sources/annotations/cn/iocoder/yudao/module/system/convert/auth/AuthConvertImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/generated-sources/annotations/cn/iocoder/yudao/module/system/convert/auth/AuthConvertImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/generated-sources/annotations/cn/iocoder/yudao/module/system/convert/oauth2/OAuth2OpenConvertImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/generated-sources/annotations/cn/iocoder/yudao/module/system/convert/oauth2/OAuth2OpenConvertImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/generated-sources/annotations/cn/iocoder/yudao/module/system/convert/tenant/TenantConvertImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/generated-sources/annotations/cn/iocoder/yudao/module/system/convert/tenant/TenantConvertImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/generated-sources/annotations/cn/iocoder/yudao/module/system/convert/user/UserConvertImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/generated-sources/annotations/cn/iocoder/yudao/module/system/convert/user/UserConvertImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-server/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-server/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-server/src/main/resources/application-dev.yaml" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-server/src/main/resources/application-dev.yaml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-server/src/main/resources/application-local.yaml" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-server/src/main/resources/application-local.yaml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-server/src/main/resources/application.yaml" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-server/src/main/resources/application.yaml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-server/src/test/java/cn/iocoder/yudao/ProjectReactor.java" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-server/src/test/java/cn/iocoder/yudao/ProjectReactor.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-server/target/classes/application-dev.yaml" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-server/target/classes/application-dev.yaml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-server/target/classes/application-local.yaml" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-server/target/classes/application-local.yaml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-server/target/classes/application.yaml" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-server/target/classes/application.yaml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-server/target/classes/cn/iocoder/yudao/server/YudaoServerApplication.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-server/target/classes/cn/iocoder/yudao/server/YudaoServerApplication.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-server/target/classes/cn/iocoder/yudao/server/controller/DefaultController.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-server/target/classes/cn/iocoder/yudao/server/controller/DefaultController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-ui/yudao-ui-admin-vue2/README.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-ui/yudao-ui-admin-vue3/README.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-ui/yudao-ui-mall-uniapp/README.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../yudao-ui-admin-vben/apps/web-ele/.env" beforeDir="false" afterPath="$PROJECT_DIR$/../yudao-ui-admin-vben/apps/web-ele/.env" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../yudao-ui-admin-vben/apps/web-ele/.env.development" beforeDir="false" afterPath="$PROJECT_DIR$/../yudao-ui-admin-vben/apps/web-ele/.env.development" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../yudao-ui-admin-vben/apps/web-ele/.env.production" beforeDir="false" afterPath="$PROJECT_DIR$/../yudao-ui-admin-vben/apps/web-ele/.env.production" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../yudao-ui-admin-vben/apps/web-ele/src/api/index.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../yudao-ui-admin-vben/apps/web-ele/src/api/index.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../yudao-ui-admin-vben/apps/web-ele/src/api/request.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../yudao-ui-admin-vben/apps/web-ele/src/api/request.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../yudao-ui-admin-vben/apps/web-ele/src/layouts/basic.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../yudao-ui-admin-vben/apps/web-ele/src/layouts/basic.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../yudao-ui-admin-vben/apps/web-ele/src/main.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../yudao-ui-admin-vben/apps/web-ele/src/main.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../yudao-ui-admin-vben/apps/web-ele/src/utils/constants.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../yudao-ui-admin-vben/apps/web-ele/src/utils/constants.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../yudao-ui-admin-vben/apps/web-ele/src/utils/formatTime.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../yudao-ui-admin-vben/apps/web-ele/src/utils/formatTime.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../yudao-ui-admin-vben/apps/web-ele/src/views/_core/authentication/login.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../yudao-ui-admin-vben/apps/web-ele/src/views/_core/authentication/login.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../yudao-ui-admin-vben/apps/web-ele/src/views/dashboard/analytics/analytics-trends.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../yudao-ui-admin-vben/apps/web-ele/src/views/dashboard/analytics/analytics-trends.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../yudao-ui-admin-vben/apps/web-ele/src/views/dashboard/analytics/analytics-visits-data.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../yudao-ui-admin-vben/apps/web-ele/src/views/dashboard/analytics/analytics-visits-data.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../yudao-ui-admin-vben/apps/web-ele/src/views/dashboard/analytics/analytics-visits-sales.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../yudao-ui-admin-vben/apps/web-ele/src/views/dashboard/analytics/analytics-visits-sales.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../yudao-ui-admin-vben/apps/web-ele/src/views/dashboard/analytics/analytics-visits-source.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../yudao-ui-admin-vben/apps/web-ele/src/views/dashboard/analytics/analytics-visits-source.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../yudao-ui-admin-vben/apps/web-ele/src/views/dashboard/analytics/analytics-visits.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../yudao-ui-admin-vben/apps/web-ele/src/views/dashboard/analytics/analytics-visits.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../yudao-ui-admin-vben/apps/web-ele/src/views/dashboard/analytics/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../yudao-ui-admin-vben/apps/web-ele/src/views/dashboard/analytics/index.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../yudao-ui-admin-vben/apps/web-ele/src/views/dashboard/workspace/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../yudao-ui-admin-vben/apps/web-ele/src/views/dashboard/workspace/index.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../yudao-ui-admin-vben/apps/web-ele/src/views/system/mail/account/data.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../yudao-ui-admin-vben/apps/web-ele/src/views/system/mail/account/data.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../yudao-ui-admin-vben/apps/web-ele/src/views/system/mail/account/modules/form.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../yudao-ui-admin-vben/apps/web-ele/src/views/system/mail/account/modules/form.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../yudao-ui-admin-vben/packages/@core/base/shared/src/constants/vben.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../yudao-ui-admin-vben/packages/@core/base/shared/src/constants/vben.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../yudao-ui-admin-vben/packages/@core/preferences/src/config.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../yudao-ui-admin-vben/packages/@core/preferences/src/config.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../yudao-ui-admin-vben/packages/@core/ui-kit/form-ui/src/form-api.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../yudao-ui-admin-vben/packages/@core/ui-kit/form-ui/src/form-api.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../yudao-ui-admin-vben/packages/effects/common-ui/src/ui/authentication/login.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../yudao-ui-admin-vben/packages/effects/common-ui/src/ui/authentication/login.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../yudao-ui-admin-vben/pnpm-lock.yaml" beforeDir="false" afterPath="$PROJECT_DIR$/../yudao-ui-admin-vben/pnpm-lock.yaml" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 6
}</component>
  <component name="ProjectId" id="308HGNk5r7qyrHhsHcPjkw25j16" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ASKED_SHARE_PROJECT_CONFIGURATION_FILES": "true",
    "Maven.yudao [clean].executor": "Run",
    "Maven.yudao [compile].executor": "Run",
    "Maven.yudao [install].executor": "Run",
    "Maven.yudao [package].executor": "Run",
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "Notification.DisplayName-DoNotAsk-DatabaseConfigFileWatcher.found": "找到数据库连接形参",
    "Notification.DoNotAsk-DatabaseConfigFileWatcher.found": "true",
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "Spring Boot.YudaoServerApplication.executor": "Debug",
    "git-widget-placeholder": "master",
    "junie.onboarding.icon.badge.shown": "true",
    "last_opened_file_path": "D:/yudao-master/yudao-boot-mini",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "project.structure.last.edited": "模块",
    "project.structure.proportion": "0.15",
    "project.structure.side.proportion": "0.28735632",
    "settings.editor.selected.configurable": "preferences.pluginManager",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RunManager">
    <configuration default="true" type="JetRunConfigurationType">
      <module name="yudao-boot-mini" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="KotlinStandaloneScriptRunConfigurationType">
      <module name="yudao-boot-mini" />
      <option name="filePath" />
      <method v="2" />
    </configuration>
    <configuration name="YudaoServerApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="yudao-server" />
      <option name="SHORTEN_COMMAND_LINE" value="CLASSPATH_FILE" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="cn.iocoder.yudao.server.YudaoServerApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.26927.53" />
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-IU-251.26927.53" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="b4656c92-87c5-4a1d-ad6c-49f0cd918aa8" name="更改" comment="" />
      <created>1753002182928</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1753002182928</updated>
      <workItem from="1753002184186" duration="3149000" />
      <workItem from="1753005411760" duration="2250000" />
      <workItem from="1753146338910" duration="1555000" />
      <workItem from="1753153334358" duration="10771000" />
      <workItem from="1753240711200" duration="7982000" />
      <workItem from="1753345514066" duration="5544000" />
      <workItem from="1753685520307" duration="752000" />
      <workItem from="1754012267555" duration="21887000" />
      <workItem from="1754183419293" duration="10005000" />
      <workItem from="1754205022788" duration="7895000" />
      <workItem from="1754271867045" duration="2499000" />
      <workItem from="1754300997813" duration="344000" />
      <workItem from="1754301354771" duration="1155000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/yudao-module-qualification/yudao-module-qualification-biz/src/main/java/cn/iocoder/yudao/module/qualification/controller/admin/summary/QualificationSummaryController.java</url>
          <line>134</line>
          <option name="timeStamp" value="7" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>