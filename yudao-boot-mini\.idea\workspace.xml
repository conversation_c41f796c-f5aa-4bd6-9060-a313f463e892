<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="b4656c92-87c5-4a1d-ad6c-49f0cd918aa8" name="更改" comment="">
      <change beforePath="$PROJECT_DIR$/../.claude/settings.local.json" beforeDir="false" afterPath="$PROJECT_DIR$/../.claude/settings.local.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../shrimp-rules.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/script/docker/docker-compose.yml" beforeDir="false" afterPath="$PROJECT_DIR$/script/docker/docker-compose.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/script/docker/docker.env" beforeDir="false" afterPath="$PROJECT_DIR$/script/docker/docker.env" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/sql/mysql/ruoyi-vue-pro.sql" beforeDir="false" afterPath="$PROJECT_DIR$/sql/mysql/ruoyi-vue-pro.sql" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-tenant/target/classes/META-INF/spring-configuration-metadata.json" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-biz-tenant/target/classes/META-INF/spring-configuration-metadata.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-excel/src/main/java/cn/iocoder/yudao/framework/excel/core/util/ExcelUtils.java" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-excel/src/main/java/cn/iocoder/yudao/framework/excel/core/util/ExcelUtils.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-excel/target/classes/cn/iocoder/yudao/framework/excel/core/util/ExcelUtils.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-excel/target/classes/cn/iocoder/yudao/framework/excel/core/util/ExcelUtils.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-redis/target/classes/META-INF/spring-configuration-metadata.json" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-redis/target/classes/META-INF/spring-configuration-metadata.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-security/target/classes/META-INF/spring-configuration-metadata.json" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-security/target/classes/META-INF/spring-configuration-metadata.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/META-INF/spring-configuration-metadata.json" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-web/target/classes/META-INF/spring-configuration-metadata.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/META-INF/spring-configuration-metadata.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/config/WebSocketProperties.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/config/YudaoWebSocketAutoConfiguration$KafkaWebSocketMessageSenderConfiguration.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/config/YudaoWebSocketAutoConfiguration$LocalWebSocketMessageSenderConfiguration.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/config/YudaoWebSocketAutoConfiguration$RabbitMQWebSocketMessageSenderConfiguration.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/config/YudaoWebSocketAutoConfiguration$RedisWebSocketMessageSenderConfiguration.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/config/YudaoWebSocketAutoConfiguration$RocketMQWebSocketMessageSenderConfiguration.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/config/YudaoWebSocketAutoConfiguration.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/core/handler/JsonWebSocketMessageHandler.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/core/listener/WebSocketMessageListener.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/core/message/JsonWebSocketMessage.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/core/security/LoginUserHandshakeInterceptor.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/core/security/WebSocketAuthorizeRequestsCustomizer.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/core/sender/AbstractWebSocketMessageSender.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/core/sender/WebSocketMessageSender.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/core/sender/kafka/KafkaWebSocketMessage.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/core/sender/kafka/KafkaWebSocketMessageConsumer.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/core/sender/kafka/KafkaWebSocketMessageSender.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/core/sender/local/LocalWebSocketMessageSender.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/core/sender/rabbitmq/RabbitMQWebSocketMessage.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/core/sender/rabbitmq/RabbitMQWebSocketMessageConsumer.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/core/sender/rabbitmq/RabbitMQWebSocketMessageSender.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/core/sender/redis/RedisWebSocketMessage.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/core/sender/redis/RedisWebSocketMessageConsumer.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/core/sender/redis/RedisWebSocketMessageSender.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/core/sender/rocketmq/RocketMQWebSocketMessage.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/core/sender/rocketmq/RocketMQWebSocketMessageConsumer.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/core/sender/rocketmq/RocketMQWebSocketMessageSender.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/core/session/WebSocketSessionHandlerDecorator.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/core/session/WebSocketSessionManager.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/core/session/WebSocketSessionManagerImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-websocket/target/classes/cn/iocoder/yudao/framework/websocket/core/util/WebSocketFrameworkUtils.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/META-INF/spring-configuration-metadata.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/api/config/ConfigApi.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/api/config/ConfigApiImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/api/file/FileApi.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/api/file/FileApiImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/api/logger/ApiAccessLogApiImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/api/logger/ApiErrorLogApiImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/api/websocket/WebSocketSenderApi.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/api/websocket/WebSocketSenderApiImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/convert/codegen/CodegenConvert.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/convert/codegen/CodegenConvertImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/convert/codegen/CodegenConvertImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/convert/config/ConfigConvert.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/convert/config/ConfigConvertImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/convert/config/ConfigConvertImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/convert/file/FileConfigConvert.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/convert/file/FileConfigConvertImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/convert/file/FileConfigConvertImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/convert/redis/RedisConvert.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/convert/redis/RedisConvertImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/convert/redis/RedisConvertImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/codegen/CodegenColumnDO.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/codegen/CodegenTableDO.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/config/ConfigDO.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/db/DataSourceConfigDO.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/demo/demo01/Demo01ContactDO$Demo01ContactDOBuilder.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/demo/demo01/Demo01ContactDO.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/demo/demo02/Demo02CategoryDO$Demo02CategoryDOBuilder.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/demo/demo02/Demo02CategoryDO.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/demo/demo03/Demo03CourseDO$Demo03CourseDOBuilder.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/demo/demo03/Demo03CourseDO.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/demo/demo03/Demo03GradeDO$Demo03GradeDOBuilder.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/demo/demo03/Demo03GradeDO.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/demo/demo03/Demo03StudentDO$Demo03StudentDOBuilder.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/demo/demo03/Demo03StudentDO.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/file/FileConfigDO$FileClientConfigTypeHandler$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/file/FileConfigDO$FileClientConfigTypeHandler.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/file/FileConfigDO$FileConfigDOBuilder.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/file/FileConfigDO.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/file/FileContentDO$FileContentDOBuilder.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/file/FileContentDO.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/file/FileDO$FileDOBuilder.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/file/FileDO.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/job/JobDO$JobDOBuilder.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/job/JobDO.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/job/JobLogDO$JobLogDOBuilder.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/job/JobLogDO.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/logger/ApiAccessLogDO$ApiAccessLogDOBuilder.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/logger/ApiAccessLogDO.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/logger/ApiErrorLogDO$ApiErrorLogDOBuilder.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/dataobject/logger/ApiErrorLogDO.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/mysql/codegen/CodegenColumnMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/mysql/codegen/CodegenTableMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/mysql/config/ConfigMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/mysql/db/DataSourceConfigMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/mysql/demo/demo01/Demo01ContactMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/mysql/demo/demo02/Demo02CategoryMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/mysql/demo/demo03/erp/Demo03CourseErpMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/mysql/demo/demo03/erp/Demo03GradeErpMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/mysql/demo/demo03/erp/Demo03StudentErpMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/mysql/demo/demo03/inner/Demo03CourseInnerMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/mysql/demo/demo03/inner/Demo03GradeInnerMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/mysql/demo/demo03/inner/Demo03StudentInnerMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/mysql/demo/demo03/normal/Demo03CourseNormalMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/mysql/demo/demo03/normal/Demo03GradeNormalMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/mysql/demo/demo03/normal/Demo03StudentNormalMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/mysql/file/FileConfigMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/mysql/file/FileContentMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/mysql/file/FileMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/mysql/job/JobLogMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/mysql/job/JobMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/mysql/logger/ApiAccessLogMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/dal/mysql/logger/ApiErrorLogMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/enums/DictTypeConstants.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/enums/ErrorCodeConstants.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/enums/codegen/CodegenColumnHtmlTypeEnum.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/enums/codegen/CodegenColumnListConditionEnum.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/enums/codegen/CodegenFrontTypeEnum.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/enums/codegen/CodegenSceneEnum.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/enums/codegen/CodegenTemplateTypeEnum.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/enums/codegen/CodegenVOTypeEnum.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/enums/config/ConfigTypeEnum.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/enums/job/JobLogStatusEnum.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/enums/job/JobStatusEnum.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/enums/logger/ApiErrorLogProcessStatusEnum.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/job/job/JobLogCleanJob.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/job/logger/AccessLogCleanJob.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/job/logger/ErrorLogCleanJob.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/codegen/CodegenService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/codegen/CodegenServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/codegen/inner/CodegenBuilder.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/codegen/inner/CodegenEngine.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/config/ConfigService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/config/ConfigServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/db/DataSourceConfigService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/db/DataSourceConfigServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/db/DatabaseTableService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/db/DatabaseTableServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/demo/demo01/Demo01ContactService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/demo/demo01/Demo01ContactServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/demo/demo02/Demo02CategoryService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/demo/demo02/Demo02CategoryServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/demo/demo03/erp/Demo03StudentErpService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/demo/demo03/erp/Demo03StudentErpServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/demo/demo03/inner/Demo03StudentInnerService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/demo/demo03/inner/Demo03StudentInnerServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/demo/demo03/normal/Demo03StudentNormalService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/demo/demo03/normal/Demo03StudentNormalServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/file/FileConfigService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/file/FileConfigServiceImpl$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/file/FileConfigServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/file/FileService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/file/FileServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/job/JobLogService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/job/JobLogServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/job/JobService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/job/JobServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/logger/ApiAccessLogService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/logger/ApiAccessLogServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/logger/ApiErrorLogService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/service/logger/ApiErrorLogServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/websocket/DemoWebSocketMessageListener.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/websocket/message/DemoReceiveMessage.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/classes/cn/iocoder/yudao/module/infra/websocket/message/DemoSendMessage.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/generated-sources/annotations/cn/iocoder/yudao/module/infra/convert/codegen/CodegenConvertImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/generated-sources/annotations/cn/iocoder/yudao/module/infra/convert/codegen/CodegenConvertImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/generated-sources/annotations/cn/iocoder/yudao/module/infra/convert/config/ConfigConvertImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/generated-sources/annotations/cn/iocoder/yudao/module/infra/convert/config/ConfigConvertImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/generated-sources/annotations/cn/iocoder/yudao/module/infra/convert/file/FileConfigConvertImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/generated-sources/annotations/cn/iocoder/yudao/module/infra/convert/file/FileConfigConvertImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-infra/target/generated-sources/annotations/cn/iocoder/yudao/module/infra/convert/redis/RedisConvertImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-infra/target/generated-sources/annotations/cn/iocoder/yudao/module/infra/convert/redis/RedisConvertImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/src/main/java/cn/iocoder/yudao/module/system/controller/admin/permission/vo/permission/PermissionAssignRoleDataScopeReqVO.java" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/src/main/java/cn/iocoder/yudao/module/system/controller/admin/permission/vo/permission/PermissionAssignRoleDataScopeReqVO.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/src/main/java/cn/iocoder/yudao/module/system/controller/admin/permission/vo/permission/PermissionAssignRoleMenuReqVO.java" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/src/main/java/cn/iocoder/yudao/module/system/controller/admin/permission/vo/permission/PermissionAssignRoleMenuReqVO.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/src/main/java/cn/iocoder/yudao/module/system/controller/admin/permission/vo/permission/PermissionAssignUserRoleReqVO.java" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/src/main/java/cn/iocoder/yudao/module/system/controller/admin/permission/vo/permission/PermissionAssignUserRoleReqVO.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/META-INF/spring-configuration-metadata.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/permission/vo/permission/PermissionAssignRoleDataScopeReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/permission/vo/permission/PermissionAssignRoleDataScopeReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/permission/vo/permission/PermissionAssignRoleMenuReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/permission/vo/permission/PermissionAssignRoleMenuReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/permission/vo/permission/PermissionAssignUserRoleReqVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/classes/cn/iocoder/yudao/module/system/controller/admin/permission/vo/permission/PermissionAssignUserRoleReqVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/generated-sources/annotations/cn/iocoder/yudao/module/system/convert/auth/AuthConvertImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/generated-sources/annotations/cn/iocoder/yudao/module/system/convert/auth/AuthConvertImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/generated-sources/annotations/cn/iocoder/yudao/module/system/convert/oauth2/OAuth2OpenConvertImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/generated-sources/annotations/cn/iocoder/yudao/module/system/convert/oauth2/OAuth2OpenConvertImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/generated-sources/annotations/cn/iocoder/yudao/module/system/convert/tenant/TenantConvertImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/generated-sources/annotations/cn/iocoder/yudao/module/system/convert/tenant/TenantConvertImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-module-system/target/generated-sources/annotations/cn/iocoder/yudao/module/system/convert/user/UserConvertImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-system/target/generated-sources/annotations/cn/iocoder/yudao/module/system/convert/user/UserConvertImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-server/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-server/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-server/src/main/resources/application-dev.yaml" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-server/src/main/resources/application-dev.yaml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-server/src/main/resources/application-local.yaml" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-server/src/main/resources/application-local.yaml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-server/src/main/resources/application.yaml" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-server/src/main/resources/application.yaml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-server/src/test/java/cn/iocoder/yudao/ProjectReactor.java" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-server/src/test/java/cn/iocoder/yudao/ProjectReactor.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-server/target/classes/application-dev.yaml" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-server/target/classes/application-dev.yaml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-server/target/classes/application-local.yaml" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-server/target/classes/application-local.yaml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-server/target/classes/application.yaml" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-server/target/classes/application.yaml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-server/target/classes/cn/iocoder/yudao/server/YudaoServerApplication.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-server/target/classes/cn/iocoder/yudao/server/controller/DefaultController.class" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-server/target/classes/cn/iocoder/yudao/server/controller/DefaultController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-server/target/classes/logback-spring.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-ui/yudao-ui-admin-vue2/README.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-ui/yudao-ui-admin-vue3/README.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/yudao-ui/yudao-ui-mall-uniapp/README.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../yudao-ui-admin-vben/apps/web-ele/.env" beforeDir="false" afterPath="$PROJECT_DIR$/../yudao-ui-admin-vben/apps/web-ele/.env" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../yudao-ui-admin-vben/apps/web-ele/.env.development" beforeDir="false" afterPath="$PROJECT_DIR$/../yudao-ui-admin-vben/apps/web-ele/.env.development" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../yudao-ui-admin-vben/apps/web-ele/.env.production" beforeDir="false" afterPath="$PROJECT_DIR$/../yudao-ui-admin-vben/apps/web-ele/.env.production" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../yudao-ui-admin-vben/apps/web-ele/src/api/index.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../yudao-ui-admin-vben/apps/web-ele/src/api/index.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../yudao-ui-admin-vben/apps/web-ele/src/api/request.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../yudao-ui-admin-vben/apps/web-ele/src/api/request.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../yudao-ui-admin-vben/apps/web-ele/src/layouts/basic.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../yudao-ui-admin-vben/apps/web-ele/src/layouts/basic.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../yudao-ui-admin-vben/apps/web-ele/src/main.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../yudao-ui-admin-vben/apps/web-ele/src/main.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../yudao-ui-admin-vben/apps/web-ele/src/utils/constants.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../yudao-ui-admin-vben/apps/web-ele/src/utils/constants.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../yudao-ui-admin-vben/apps/web-ele/src/utils/formatTime.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../yudao-ui-admin-vben/apps/web-ele/src/utils/formatTime.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../yudao-ui-admin-vben/apps/web-ele/src/views/_core/authentication/login.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../yudao-ui-admin-vben/apps/web-ele/src/views/_core/authentication/login.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../yudao-ui-admin-vben/apps/web-ele/src/views/dashboard/analytics/analytics-trends.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../yudao-ui-admin-vben/apps/web-ele/src/views/dashboard/analytics/analytics-trends.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../yudao-ui-admin-vben/apps/web-ele/src/views/dashboard/analytics/analytics-visits-data.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../yudao-ui-admin-vben/apps/web-ele/src/views/dashboard/analytics/analytics-visits-data.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../yudao-ui-admin-vben/apps/web-ele/src/views/dashboard/analytics/analytics-visits-sales.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../yudao-ui-admin-vben/apps/web-ele/src/views/dashboard/analytics/analytics-visits-sales.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../yudao-ui-admin-vben/apps/web-ele/src/views/dashboard/analytics/analytics-visits-source.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../yudao-ui-admin-vben/apps/web-ele/src/views/dashboard/analytics/analytics-visits-source.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../yudao-ui-admin-vben/apps/web-ele/src/views/dashboard/analytics/analytics-visits.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../yudao-ui-admin-vben/apps/web-ele/src/views/dashboard/analytics/analytics-visits.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../yudao-ui-admin-vben/apps/web-ele/src/views/dashboard/analytics/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../yudao-ui-admin-vben/apps/web-ele/src/views/dashboard/analytics/index.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../yudao-ui-admin-vben/apps/web-ele/src/views/dashboard/workspace/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../yudao-ui-admin-vben/apps/web-ele/src/views/dashboard/workspace/index.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../yudao-ui-admin-vben/apps/web-ele/src/views/system/mail/account/data.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../yudao-ui-admin-vben/apps/web-ele/src/views/system/mail/account/data.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../yudao-ui-admin-vben/apps/web-ele/src/views/system/mail/account/modules/form.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../yudao-ui-admin-vben/apps/web-ele/src/views/system/mail/account/modules/form.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../yudao-ui-admin-vben/packages/@core/base/shared/src/constants/vben.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../yudao-ui-admin-vben/packages/@core/base/shared/src/constants/vben.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../yudao-ui-admin-vben/packages/@core/preferences/src/config.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../yudao-ui-admin-vben/packages/@core/preferences/src/config.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../yudao-ui-admin-vben/packages/@core/ui-kit/form-ui/src/form-api.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../yudao-ui-admin-vben/packages/@core/ui-kit/form-ui/src/form-api.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../yudao-ui-admin-vben/packages/effects/common-ui/src/ui/authentication/login.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../yudao-ui-admin-vben/packages/effects/common-ui/src/ui/authentication/login.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../yudao-ui-admin-vben/pnpm-lock.yaml" beforeDir="false" afterPath="$PROJECT_DIR$/../yudao-ui-admin-vben/pnpm-lock.yaml" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 6
}</component>
  <component name="ProjectId" id="308HGNk5r7qyrHhsHcPjkw25j16" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ASKED_SHARE_PROJECT_CONFIGURATION_FILES": "true",
    "Maven.yudao [clean].executor": "Run",
    "Maven.yudao [compile].executor": "Run",
    "Maven.yudao [install].executor": "Run",
    "Maven.yudao [package].executor": "Run",
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "Notification.DisplayName-DoNotAsk-DatabaseConfigFileWatcher.found": "找到数据库连接形参",
    "Notification.DoNotAsk-DatabaseConfigFileWatcher.found": "true",
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "Spring Boot.YudaoServerApplication.executor": "Debug",
    "git-widget-placeholder": "master",
    "junie.onboarding.icon.badge.shown": "true",
    "last_opened_file_path": "D:/yudao-master/yudao-boot-mini",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "project.structure.last.edited": "模块",
    "project.structure.proportion": "0.15",
    "project.structure.side.proportion": "0.28735632",
    "settings.editor.selected.configurable": "preferences.pluginManager",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RunManager">
    <configuration default="true" type="JetRunConfigurationType">
      <module name="yudao-boot-mini" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="KotlinStandaloneScriptRunConfigurationType">
      <module name="yudao-boot-mini" />
      <option name="filePath" />
      <method v="2" />
    </configuration>
    <configuration name="YudaoServerApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="yudao-server" />
      <option name="SHORTEN_COMMAND_LINE" value="CLASSPATH_FILE" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="cn.iocoder.yudao.server.YudaoServerApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.26927.53" />
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-IU-251.26927.53" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="b4656c92-87c5-4a1d-ad6c-49f0cd918aa8" name="更改" comment="" />
      <created>1753002182928</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1753002182928</updated>
      <workItem from="1753002184186" duration="3149000" />
      <workItem from="1753005411760" duration="2250000" />
      <workItem from="1753146338910" duration="1555000" />
      <workItem from="1753153334358" duration="10771000" />
      <workItem from="1753240711200" duration="7982000" />
      <workItem from="1753345514066" duration="5544000" />
      <workItem from="1753685520307" duration="752000" />
      <workItem from="1754012267555" duration="21887000" />
      <workItem from="1754183419293" duration="10005000" />
      <workItem from="1754205022788" duration="7895000" />
      <workItem from="1754271867045" duration="2499000" />
      <workItem from="1754300997813" duration="344000" />
      <workItem from="1754301354771" duration="1808000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/yudao-module-qualification/yudao-module-qualification-biz/src/main/java/cn/iocoder/yudao/module/qualification/controller/admin/summary/QualificationSummaryController.java</url>
          <line>134</line>
          <option name="timeStamp" value="7" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>