package cn.iocoder.yudao.framework.common.util.json.databind;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonToken;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;

import java.io.IOException;
import java.util.Collections;
import java.util.HashSet;
import java.util.Set;

/**
 * Long Set 反序列化器
 * 
 * 支持将单个Long值或Long数组反序列化为Set<Long>
 * 解决前端发送单个值时Jackson无法正确反序列化为Set的问题
 *
 * <AUTHOR>
 */
public class LongSetDeserializer extends JsonDeserializer<Set<Long>> {

    public static final LongSetDeserializer INSTANCE = new LongSetDeserializer();

    @Override
    public Set<Long> deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        JsonToken token = p.getCurrentToken();
        
        if (token == JsonToken.VALUE_NULL) {
            return Collections.emptySet();
        }
        
        Set<Long> result = new HashSet<>();
        
        if (token == JsonToken.VALUE_NUMBER_INT) {
            // 单个数值
            result.add(p.getLongValue());
        } else if (token == JsonToken.START_ARRAY) {
            // 数组
            while (p.nextToken() != JsonToken.END_ARRAY) {
                if (p.getCurrentToken() == JsonToken.VALUE_NUMBER_INT) {
                    result.add(p.getLongValue());
                } else if (p.getCurrentToken() == JsonToken.VALUE_STRING) {
                    // 支持字符串形式的数字
                    String value = p.getText();
                    if (value != null && !value.trim().isEmpty()) {
                        try {
                            result.add(Long.parseLong(value.trim()));
                        } catch (NumberFormatException e) {
                            // 忽略无法解析的值
                        }
                    }
                }
            }
        } else if (token == JsonToken.VALUE_STRING) {
            // 字符串形式的单个值
            String value = p.getText();
            if (value != null && !value.trim().isEmpty()) {
                try {
                    result.add(Long.parseLong(value.trim()));
                } catch (NumberFormatException e) {
                    // 忽略无法解析的值
                }
            }
        }
        
        return result;
    }
}
