package cn.iocoder.yudao.module.material.convert;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.material.controller.admin.category.vo.MaterialCategoryRespVO;
import cn.iocoder.yudao.module.material.controller.admin.category.vo.MaterialCategorySaveReqVO;
import cn.iocoder.yudao.module.material.dal.dataobject.MaterialCategoryDO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T17:48:14+0800",
    comments = "version: 1.6.3, compiler: Eclipse JDT (IDE) 3.42.50.v20250729-0351, environment: Java 21.0.8 (Eclipse Adoptium)"
)
public class MaterialCategoryConvertImpl implements MaterialCategoryConvert {

    @Override
    public MaterialCategoryDO convert(MaterialCategorySaveReqVO bean) {
        if ( bean == null ) {
            return null;
        }

        MaterialCategoryDO.MaterialCategoryDOBuilder materialCategoryDO = MaterialCategoryDO.builder();

        materialCategoryDO.code( bean.getCode() );
        materialCategoryDO.description( bean.getDescription() );
        materialCategoryDO.id( bean.getId() );
        materialCategoryDO.name( bean.getName() );
        materialCategoryDO.parentCode( bean.getParentCode() );
        materialCategoryDO.parentId( bean.getParentId() );
        materialCategoryDO.parentName( bean.getParentName() );
        materialCategoryDO.sort( bean.getSort() );
        materialCategoryDO.standardCode( bean.getStandardCode() );
        materialCategoryDO.standardName( bean.getStandardName() );
        materialCategoryDO.status( bean.getStatus() );

        return materialCategoryDO.build();
    }

    @Override
    public MaterialCategoryRespVO convert(MaterialCategoryDO bean) {
        if ( bean == null ) {
            return null;
        }

        MaterialCategoryRespVO materialCategoryRespVO = new MaterialCategoryRespVO();

        materialCategoryRespVO.setChildren( convertList( bean.getChildren() ) );
        materialCategoryRespVO.setCode( bean.getCode() );
        materialCategoryRespVO.setCreateTime( bean.getCreateTime() );
        materialCategoryRespVO.setDescription( bean.getDescription() );
        materialCategoryRespVO.setId( bean.getId() );
        materialCategoryRespVO.setName( bean.getName() );
        materialCategoryRespVO.setParentCode( bean.getParentCode() );
        materialCategoryRespVO.setParentId( bean.getParentId() );
        materialCategoryRespVO.setParentName( bean.getParentName() );
        materialCategoryRespVO.setSort( bean.getSort() );
        materialCategoryRespVO.setStandardCode( bean.getStandardCode() );
        materialCategoryRespVO.setStandardName( bean.getStandardName() );
        materialCategoryRespVO.setStatus( bean.getStatus() );

        return materialCategoryRespVO;
    }

    @Override
    public List<MaterialCategoryRespVO> convertList(List<MaterialCategoryDO> list) {
        if ( list == null ) {
            return null;
        }

        List<MaterialCategoryRespVO> list1 = new ArrayList<MaterialCategoryRespVO>( list.size() );
        for ( MaterialCategoryDO materialCategoryDO : list ) {
            list1.add( convert( materialCategoryDO ) );
        }

        return list1;
    }

    @Override
    public PageResult<MaterialCategoryRespVO> convertPage(PageResult<MaterialCategoryDO> page) {
        if ( page == null ) {
            return null;
        }

        PageResult<MaterialCategoryRespVO> pageResult = new PageResult<MaterialCategoryRespVO>();

        pageResult.setList( convertList( page.getList() ) );
        pageResult.setTotal( page.getTotal() );

        return pageResult;
    }
}
