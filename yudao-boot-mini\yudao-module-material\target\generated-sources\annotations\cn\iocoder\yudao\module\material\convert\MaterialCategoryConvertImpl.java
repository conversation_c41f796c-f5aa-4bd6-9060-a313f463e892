package cn.iocoder.yudao.module.material.convert;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.material.controller.admin.category.vo.MaterialCategoryRespVO;
import cn.iocoder.yudao.module.material.controller.admin.category.vo.MaterialCategorySaveReqVO;
import cn.iocoder.yudao.module.material.dal.dataobject.MaterialCategoryDO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T18:20:19+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 1.8.0_452 (Amazon.com Inc.)"
)
public class MaterialCategoryConvertImpl implements MaterialCategoryConvert {

    @Override
    public MaterialCategoryDO convert(MaterialCategorySaveReqVO bean) {
        if ( bean == null ) {
            return null;
        }

        MaterialCategoryDO.MaterialCategoryDOBuilder materialCategoryDO = MaterialCategoryDO.builder();

        materialCategoryDO.id( bean.getId() );
        materialCategoryDO.code( bean.getCode() );
        materialCategoryDO.name( bean.getName() );
        materialCategoryDO.parentId( bean.getParentId() );
        materialCategoryDO.parentCode( bean.getParentCode() );
        materialCategoryDO.parentName( bean.getParentName() );
        materialCategoryDO.standardCode( bean.getStandardCode() );
        materialCategoryDO.standardName( bean.getStandardName() );
        materialCategoryDO.description( bean.getDescription() );
        materialCategoryDO.sort( bean.getSort() );
        materialCategoryDO.status( bean.getStatus() );

        return materialCategoryDO.build();
    }

    @Override
    public MaterialCategoryRespVO convert(MaterialCategoryDO bean) {
        if ( bean == null ) {
            return null;
        }

        MaterialCategoryRespVO materialCategoryRespVO = new MaterialCategoryRespVO();

        materialCategoryRespVO.setId( bean.getId() );
        materialCategoryRespVO.setCode( bean.getCode() );
        materialCategoryRespVO.setName( bean.getName() );
        materialCategoryRespVO.setParentId( bean.getParentId() );
        materialCategoryRespVO.setParentCode( bean.getParentCode() );
        materialCategoryRespVO.setParentName( bean.getParentName() );
        materialCategoryRespVO.setStandardCode( bean.getStandardCode() );
        materialCategoryRespVO.setStandardName( bean.getStandardName() );
        materialCategoryRespVO.setDescription( bean.getDescription() );
        materialCategoryRespVO.setSort( bean.getSort() );
        materialCategoryRespVO.setStatus( bean.getStatus() );
        materialCategoryRespVO.setCreateTime( bean.getCreateTime() );
        materialCategoryRespVO.setChildren( convertList( bean.getChildren() ) );

        return materialCategoryRespVO;
    }

    @Override
    public List<MaterialCategoryRespVO> convertList(List<MaterialCategoryDO> list) {
        if ( list == null ) {
            return null;
        }

        List<MaterialCategoryRespVO> list1 = new ArrayList<MaterialCategoryRespVO>( list.size() );
        for ( MaterialCategoryDO materialCategoryDO : list ) {
            list1.add( convert( materialCategoryDO ) );
        }

        return list1;
    }

    @Override
    public PageResult<MaterialCategoryRespVO> convertPage(PageResult<MaterialCategoryDO> page) {
        if ( page == null ) {
            return null;
        }

        PageResult<MaterialCategoryRespVO> pageResult = new PageResult<MaterialCategoryRespVO>();

        pageResult.setList( convertList( page.getList() ) );
        pageResult.setTotal( page.getTotal() );

        return pageResult;
    }
}
