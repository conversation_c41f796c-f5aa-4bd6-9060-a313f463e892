package cn.iocoder.yudao.module.material.convert;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.material.controller.admin.material.vo.MaterialRespVO;
import cn.iocoder.yudao.module.material.controller.admin.material.vo.MaterialSaveReqVO;
import cn.iocoder.yudao.module.material.dal.dataobject.MaterialDO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T18:20:19+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 1.8.0_452 (Amazon.com Inc.)"
)
public class MaterialConvertImpl implements MaterialConvert {

    @Override
    public MaterialDO convert(MaterialSaveReqVO bean) {
        if ( bean == null ) {
            return null;
        }

        MaterialDO.MaterialDOBuilder materialDO = MaterialDO.builder();

        materialDO.id( bean.getId() );
        materialDO.code( bean.getCode() );
        materialDO.name( bean.getName() );
        materialDO.categoryId( bean.getCategoryId() );
        materialDO.categoryCode( bean.getCategoryCode() );
        materialDO.categoryName( bean.getCategoryName() );
        materialDO.unitCode( bean.getUnitCode() );
        materialDO.unitName( bean.getUnitName() );
        materialDO.sku( bean.getSku() );
        materialDO.brandCode( bean.getBrandCode() );
        materialDO.brandName( bean.getBrandName() );
        materialDO.shopCode( bean.getShopCode() );
        materialDO.shopName( bean.getShopName() );
        materialDO.orgCode( bean.getOrgCode() );
        materialDO.orgName( bean.getOrgName() );
        materialDO.standardCode( bean.getStandardCode() );
        materialDO.standardName( bean.getStandardName() );
        materialDO.imageUrl( bean.getImageUrl() );
        materialDO.specification( bean.getSpecification() );
        materialDO.supplier( bean.getSupplier() );
        materialDO.purchasePrice( bean.getPurchasePrice() );
        materialDO.salePrice( bean.getSalePrice() );
        materialDO.minStock( bean.getMinStock() );
        materialDO.maxStock( bean.getMaxStock() );
        materialDO.barcode( bean.getBarcode() );
        materialDO.description( bean.getDescription() );
        materialDO.remark( bean.getRemark() );
        materialDO.dataStatus( bean.getDataStatus() );
        materialDO.status( bean.getStatus() );

        return materialDO.build();
    }

    @Override
    public MaterialRespVO convert(MaterialDO bean) {
        if ( bean == null ) {
            return null;
        }

        MaterialRespVO materialRespVO = new MaterialRespVO();

        materialRespVO.setId( bean.getId() );
        materialRespVO.setCode( bean.getCode() );
        materialRespVO.setName( bean.getName() );
        materialRespVO.setCategoryId( bean.getCategoryId() );
        materialRespVO.setCategoryCode( bean.getCategoryCode() );
        materialRespVO.setCategoryName( bean.getCategoryName() );
        materialRespVO.setUnitCode( bean.getUnitCode() );
        materialRespVO.setUnitName( bean.getUnitName() );
        materialRespVO.setSku( bean.getSku() );
        materialRespVO.setBrandCode( bean.getBrandCode() );
        materialRespVO.setBrandName( bean.getBrandName() );
        materialRespVO.setShopCode( bean.getShopCode() );
        materialRespVO.setShopName( bean.getShopName() );
        materialRespVO.setOrgCode( bean.getOrgCode() );
        materialRespVO.setOrgName( bean.getOrgName() );
        materialRespVO.setStandardCode( bean.getStandardCode() );
        materialRespVO.setStandardName( bean.getStandardName() );
        materialRespVO.setImageUrl( bean.getImageUrl() );
        materialRespVO.setSpecification( bean.getSpecification() );
        materialRespVO.setSupplier( bean.getSupplier() );
        materialRespVO.setPurchasePrice( bean.getPurchasePrice() );
        materialRespVO.setSalePrice( bean.getSalePrice() );
        materialRespVO.setMinStock( bean.getMinStock() );
        materialRespVO.setMaxStock( bean.getMaxStock() );
        materialRespVO.setBarcode( bean.getBarcode() );
        materialRespVO.setDescription( bean.getDescription() );
        materialRespVO.setRemark( bean.getRemark() );
        materialRespVO.setDataStatus( bean.getDataStatus() );
        materialRespVO.setStatus( bean.getStatus() );
        materialRespVO.setCreateTime( bean.getCreateTime() );

        return materialRespVO;
    }

    @Override
    public List<MaterialRespVO> convertList(List<MaterialDO> list) {
        if ( list == null ) {
            return null;
        }

        List<MaterialRespVO> list1 = new ArrayList<MaterialRespVO>( list.size() );
        for ( MaterialDO materialDO : list ) {
            list1.add( convert( materialDO ) );
        }

        return list1;
    }

    @Override
    public PageResult<MaterialRespVO> convertPage(PageResult<MaterialDO> page) {
        if ( page == null ) {
            return null;
        }

        PageResult<MaterialRespVO> pageResult = new PageResult<MaterialRespVO>();

        pageResult.setList( convertList( page.getList() ) );
        pageResult.setTotal( page.getTotal() );

        return pageResult;
    }
}
