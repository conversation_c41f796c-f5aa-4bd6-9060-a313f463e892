package cn.iocoder.yudao.module.material.convert;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.material.controller.admin.material.vo.MaterialRespVO;
import cn.iocoder.yudao.module.material.controller.admin.material.vo.MaterialSaveReqVO;
import cn.iocoder.yudao.module.material.dal.dataobject.MaterialDO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-05T08:58:17+0800",
    comments = "version: 1.6.3, compiler: Eclipse JDT (IDE) 3.42.50.v20250729-0351, environment: Java 21.0.8 (Eclipse Adoptium)"
)
public class MaterialConvertImpl implements MaterialConvert {

    @Override
    public MaterialDO convert(MaterialSaveReqVO bean) {
        if ( bean == null ) {
            return null;
        }

        MaterialDO.MaterialDOBuilder materialDO = MaterialDO.builder();

        materialDO.barcode( bean.getBarcode() );
        materialDO.brandCode( bean.getBrandCode() );
        materialDO.brandName( bean.getBrandName() );
        materialDO.categoryCode( bean.getCategoryCode() );
        materialDO.categoryId( bean.getCategoryId() );
        materialDO.categoryName( bean.getCategoryName() );
        materialDO.code( bean.getCode() );
        materialDO.dataStatus( bean.getDataStatus() );
        materialDO.description( bean.getDescription() );
        materialDO.id( bean.getId() );
        materialDO.imageUrl( bean.getImageUrl() );
        materialDO.maxStock( bean.getMaxStock() );
        materialDO.minStock( bean.getMinStock() );
        materialDO.name( bean.getName() );
        materialDO.orgCode( bean.getOrgCode() );
        materialDO.orgName( bean.getOrgName() );
        materialDO.purchasePrice( bean.getPurchasePrice() );
        materialDO.remark( bean.getRemark() );
        materialDO.salePrice( bean.getSalePrice() );
        materialDO.shopCode( bean.getShopCode() );
        materialDO.shopName( bean.getShopName() );
        materialDO.sku( bean.getSku() );
        materialDO.specification( bean.getSpecification() );
        materialDO.standardCode( bean.getStandardCode() );
        materialDO.standardName( bean.getStandardName() );
        materialDO.status( bean.getStatus() );
        materialDO.supplier( bean.getSupplier() );
        materialDO.unitCode( bean.getUnitCode() );
        materialDO.unitName( bean.getUnitName() );

        return materialDO.build();
    }

    @Override
    public MaterialRespVO convert(MaterialDO bean) {
        if ( bean == null ) {
            return null;
        }

        MaterialRespVO materialRespVO = new MaterialRespVO();

        materialRespVO.setBarcode( bean.getBarcode() );
        materialRespVO.setBrandCode( bean.getBrandCode() );
        materialRespVO.setBrandName( bean.getBrandName() );
        materialRespVO.setCategoryCode( bean.getCategoryCode() );
        materialRespVO.setCategoryId( bean.getCategoryId() );
        materialRespVO.setCategoryName( bean.getCategoryName() );
        materialRespVO.setCode( bean.getCode() );
        materialRespVO.setCreateTime( bean.getCreateTime() );
        materialRespVO.setDataStatus( bean.getDataStatus() );
        materialRespVO.setDescription( bean.getDescription() );
        materialRespVO.setId( bean.getId() );
        materialRespVO.setImageUrl( bean.getImageUrl() );
        materialRespVO.setMaxStock( bean.getMaxStock() );
        materialRespVO.setMinStock( bean.getMinStock() );
        materialRespVO.setName( bean.getName() );
        materialRespVO.setOrgCode( bean.getOrgCode() );
        materialRespVO.setOrgName( bean.getOrgName() );
        materialRespVO.setPurchasePrice( bean.getPurchasePrice() );
        materialRespVO.setRemark( bean.getRemark() );
        materialRespVO.setSalePrice( bean.getSalePrice() );
        materialRespVO.setShopCode( bean.getShopCode() );
        materialRespVO.setShopName( bean.getShopName() );
        materialRespVO.setSku( bean.getSku() );
        materialRespVO.setSpecification( bean.getSpecification() );
        materialRespVO.setStandardCode( bean.getStandardCode() );
        materialRespVO.setStandardName( bean.getStandardName() );
        materialRespVO.setStatus( bean.getStatus() );
        materialRespVO.setSupplier( bean.getSupplier() );
        materialRespVO.setUnitCode( bean.getUnitCode() );
        materialRespVO.setUnitName( bean.getUnitName() );

        return materialRespVO;
    }

    @Override
    public List<MaterialRespVO> convertList(List<MaterialDO> list) {
        if ( list == null ) {
            return null;
        }

        List<MaterialRespVO> list1 = new ArrayList<MaterialRespVO>( list.size() );
        for ( MaterialDO materialDO : list ) {
            list1.add( convert( materialDO ) );
        }

        return list1;
    }

    @Override
    public PageResult<MaterialRespVO> convertPage(PageResult<MaterialDO> page) {
        if ( page == null ) {
            return null;
        }

        PageResult<MaterialRespVO> pageResult = new PageResult<MaterialRespVO>();

        pageResult.setList( convertList( page.getList() ) );
        pageResult.setTotal( page.getTotal() );

        return pageResult;
    }
}
