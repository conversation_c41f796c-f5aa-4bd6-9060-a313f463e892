package cn.iocoder.yudao.module.qualification.controller.admin.vcaccount;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.framework.operatelog.core.annotations.OperateLog;
import cn.iocoder.yudao.module.qualification.controller.admin.vcaccount.vo.VcAccountPageReqVO;
import cn.iocoder.yudao.module.qualification.controller.admin.vcaccount.vo.VcAccountRespVO;
import cn.iocoder.yudao.module.qualification.controller.admin.vcaccount.vo.VcAccountSaveReqVO;
import cn.iocoder.yudao.module.qualification.dal.dataobject.VcAccountDO;
import cn.iocoder.yudao.module.qualification.service.VcAccountService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.operatelog.core.enums.OperateTypeEnum.*;

@Tag(name = "管理后台 - VC账号管理")
@RestController
@RequestMapping("/qualification/vc-account")
@Validated
public class VcAccountController {

    @Resource
    private VcAccountService vcAccountService;

    @PostMapping("/create")
    @Operation(summary = "创建VC账号")
    @PreAuthorize("@ss.hasPermission('vc:account:create')")
    @OperateLog(type = CREATE)
    public CommonResult<Long> createVcAccount(@Valid @RequestBody VcAccountSaveReqVO createReqVO) {
        return success(vcAccountService.createVcAccount(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新VC账号")
    @PreAuthorize("@ss.hasPermission('vc:account:update')")
    @OperateLog(type = UPDATE)
    public CommonResult<Boolean> updateVcAccount(@Valid @RequestBody VcAccountSaveReqVO updateReqVO) {
        vcAccountService.updateVcAccount(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除VC账号")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('vc:account:delete')")
    @OperateLog(type = DELETE)
    public CommonResult<Boolean> deleteVcAccount(@RequestParam("id") Long id) {
        vcAccountService.deleteVcAccount(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得VC账号")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('vc:account:query')")
    public CommonResult<VcAccountRespVO> getVcAccount(@RequestParam("id") Long id) {
        VcAccountDO vcAccount = vcAccountService.getVcAccount(id);
        return success(BeanUtils.toBean(vcAccount, VcAccountRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得VC账号分页")
    @PreAuthorize("@ss.hasPermission('vc:account:query')")
    public CommonResult<PageResult<VcAccountRespVO>> getVcAccountPage(@Valid VcAccountPageReqVO pageReqVO) {
        PageResult<VcAccountDO> pageResult = vcAccountService.getVcAccountPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, VcAccountRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出VC账号 Excel")
    @PreAuthorize("@ss.hasPermission('vc:account:export')")
    @OperateLog(type = EXPORT)
    public void exportVcAccountExcel(@Valid VcAccountPageReqVO exportReqVO,
                                     HttpServletResponse response) throws IOException {
        exportReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<VcAccountDO> list = vcAccountService.getVcAccountList(exportReqVO);
        // 导出 Excel
        ExcelUtils.write(response, "VC账号管理.xls", "数据", VcAccountRespVO.class,
                BeanUtils.toBean(list, VcAccountRespVO.class));
    }

    @GetMapping("/simple-list")
    @Operation(summary = "获取VC账号简单列表")
    @PreAuthorize("@ss.hasPermission('vc:account:query')")
    public CommonResult<List<VcAccountRespVO>> getSimpleVcAccountList() {
        List<VcAccountDO> list = vcAccountService.getSimpleVcAccountList();
        return success(BeanUtils.toBean(list, VcAccountRespVO.class));
    }

    @GetMapping("/my-managed-accounts")
    @Operation(summary = "获取我负责的VC账号列表")
    @PreAuthorize("@ss.hasPermission('vc:account:manage-own')")
    public CommonResult<List<VcAccountRespVO>> getMyManagedAccounts() {
        // 获取当前登录用户ID
        Long userId = cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId();
        List<VcAccountDO> list = vcAccountService.getVcAccountListByUserId(userId);
        return success(BeanUtils.toBean(list, VcAccountRespVO.class));
    }

}
