package cn.iocoder.yudao.module.qualification.controller.admin.vcaccount.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - VC账号管理分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class VcAccountPageReqVO extends PageParam {

    @Schema(description = "VC账号编码", example = "TEST001")
    private String accountCode;

    @Schema(description = "VC账号名称", example = "测试VC账号")
    private String accountName;

    @Schema(description = "账号类型", example = "旗舰店")
    private String accountType;

    @Schema(description = "公司名称", example = "测试公司")
    private String companyName;

    @Schema(description = "负责人用户ID", example = "1")
    private Long managerUserId;

    @Schema(description = "负责人姓名", example = "张三")
    private String managerUserName;

    @Schema(description = "状态", example = "1")
    private Integer status;

    @Schema(description = "优先级", example = "1")
    private Integer priorityLevel;

    @Schema(description = "所属区域编码", example = "310000")
    private String regionCode;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
