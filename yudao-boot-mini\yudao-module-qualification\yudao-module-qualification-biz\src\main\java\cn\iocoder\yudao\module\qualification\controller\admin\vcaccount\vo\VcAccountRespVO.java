package cn.iocoder.yudao.module.qualification.controller.admin.vcaccount.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - VC账号管理 Response VO")
@Data
public class VcAccountRespVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long id;

    @Schema(description = "VC账号编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "TEST001")
    private String accountCode;

    @Schema(description = "VC账号名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "测试VC账号")
    private String accountName;

    @Schema(description = "账号类型", example = "旗舰店")
    private String accountType;

    @Schema(description = "公司名称", example = "测试公司有限公司")
    private String companyName;

    @Schema(description = "联系人", example = "张三")
    private String contactPerson;

    @Schema(description = "联系电话", example = "***********")
    private String contactPhone;

    @Schema(description = "联系邮箱", example = "<EMAIL>")
    private String contactEmail;

    @Schema(description = "经营范围", example = "电子产品销售")
    private String businessScope;

    @Schema(description = "所属区域编码", example = "310000")
    private String regionCode;

    @Schema(description = "所属区域名称", example = "上海市")
    private String regionName;

    @Schema(description = "负责人用户ID", example = "1")
    private Long managerUserId;

    @Schema(description = "负责人姓名", example = "张三")
    private String managerUserName;

    @Schema(description = "备用负责人用户ID", example = "2")
    private Long backupManagerUserId;

    @Schema(description = "备用负责人姓名", example = "李四")
    private String backupManagerUserName;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer status;

    @Schema(description = "优先级", example = "1")
    private Integer priorityLevel;

    @Schema(description = "备注", example = "测试账号")
    private String remark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;

    @Schema(description = "更新时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime updateTime;

    @Schema(description = "创建者", example = "admin")
    private String creator;

    @Schema(description = "更新者", example = "admin")
    private String updater;

    // 扩展字段 - 统计信息
    @Schema(description = "关联资质数量", example = "15")
    private Long qualificationCount;

    @Schema(description = "即将过期资质数量", example = "3")
    private Long expiringCount;

}
