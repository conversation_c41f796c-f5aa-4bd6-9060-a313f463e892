package cn.iocoder.yudao.module.qualification.controller.admin.vcaccount.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

@Schema(description = "管理后台 - VC账号管理新增/修改 Request VO")
@Data
public class VcAccountSaveReqVO {

    @Schema(description = "主键ID", example = "1")
    private Long id;

    @Schema(description = "VC账号编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "TEST001")
    @NotBlank(message = "VC账号编码不能为空")
    @Size(max = 100, message = "VC账号编码长度不能超过100个字符")
    @Pattern(regexp = "^[A-Z0-9_]{3,20}$", message = "VC账号编码格式不正确，只能包含大写字母、数字和下划线，长度3-20位")
    private String accountCode;

    @Schema(description = "VC账号名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "测试VC账号")
    @NotBlank(message = "VC账号名称不能为空")
    @Size(max = 200, message = "VC账号名称长度不能超过200个字符")
    private String accountName;

    @Schema(description = "账号类型", example = "旗舰店")
    @Size(max = 50, message = "账号类型长度不能超过50个字符")
    private String accountType;

    @Schema(description = "公司名称", example = "测试公司有限公司")
    @Size(max = 300, message = "公司名称长度不能超过300个字符")
    private String companyName;

    @Schema(description = "联系人", example = "张三")
    @Size(max = 100, message = "联系人长度不能超过100个字符")
    private String contactPerson;

    @Schema(description = "联系电话", example = "***********")
    @Size(max = 50, message = "联系电话长度不能超过50个字符")
    @Pattern(regexp = "^1[3-9]\\d{9}$|^0\\d{2,3}-?\\d{7,8}$|^$", message = "联系电话格式不正确")
    private String contactPhone;

    @Schema(description = "联系邮箱", example = "<EMAIL>")
    @Size(max = 100, message = "联系邮箱长度不能超过100个字符")
    @Pattern(regexp = "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$|^$", message = "联系邮箱格式不正确")
    private String contactEmail;

    @Schema(description = "经营范围", example = "电子产品销售")
    private String businessScope;

    @Schema(description = "所属区域编码", example = "310000")
    @Size(max = 50, message = "所属区域编码长度不能超过50个字符")
    private String regionCode;

    @Schema(description = "所属区域名称", example = "上海市")
    @Size(max = 100, message = "所属区域名称长度不能超过100个字符")
    private String regionName;

    @Schema(description = "负责人用户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "负责人不能为空")
    private Long managerUserId;

    @Schema(description = "备用负责人用户ID", example = "2")
    private Long backupManagerUserId;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "状态不能为空")
    private Integer status;

    @Schema(description = "优先级", example = "1")
    private Integer priorityLevel;

    @Schema(description = "备注", example = "测试账号")
    @Size(max = 500, message = "备注长度不能超过500个字符")
    private String remark;

}
