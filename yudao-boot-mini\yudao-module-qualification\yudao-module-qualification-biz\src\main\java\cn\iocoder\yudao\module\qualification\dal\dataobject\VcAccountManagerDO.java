package cn.iocoder.yudao.module.qualification.dal.dataobject;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.TenantBaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.time.LocalDateTime;

/**
 * VC账号负责人关联 DO
 *
 * <AUTHOR>
 */
@TableName("vc_account_manager")
@KeySequence("vc_account_manager_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VcAccountManagerDO extends TenantBaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;
    
    /**
     * VC账号ID
     */
    private Long vcAccountId;
    
    /**
     * 负责人用户ID
     */
    private Long userId;
    
    /**
     * 负责人姓名（冗余）
     */
    private String userName;
    
    /**
     * 负责人类型（1主负责人 2副负责人 3协助负责人）
     */
    private Integer managerType;
    
    /**
     * 分配时间
     */
    private LocalDateTime assignDate;
    
    /**
     * 状态（1正常 0停用）
     */
    private Integer status;

}
