package cn.iocoder.yudao.module.qualification.dal.mysql;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.qualification.controller.admin.vcaccount.vo.VcAccountPageReqVO;
import cn.iocoder.yudao.module.qualification.dal.dataobject.VcAccountDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * VC账号管理 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface VcAccountMapper extends BaseMapperX<VcAccountDO> {

    default PageResult<VcAccountDO> selectPage(VcAccountPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<VcAccountDO>()
                .likeIfPresent(VcAccountDO::getAccountCode, reqVO.getAccountCode())
                .likeIfPresent(VcAccountDO::getAccountName, reqVO.getAccountName())
                .eqIfPresent(VcAccountDO::getAccountType, reqVO.getAccountType())
                .likeIfPresent(VcAccountDO::getCompanyName, reqVO.getCompanyName())
                .eqIfPresent(VcAccountDO::getManagerUserId, reqVO.getManagerUserId())
                .likeIfPresent(VcAccountDO::getManagerUserName, reqVO.getManagerUserName())
                .eqIfPresent(VcAccountDO::getStatus, reqVO.getStatus())
                .eqIfPresent(VcAccountDO::getPriorityLevel, reqVO.getPriorityLevel())
                .eqIfPresent(VcAccountDO::getRegionCode, reqVO.getRegionCode())
                .betweenIfPresent(VcAccountDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(VcAccountDO::getId));
    }

    default List<VcAccountDO> selectList(VcAccountPageReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<VcAccountDO>()
                .likeIfPresent(VcAccountDO::getAccountCode, reqVO.getAccountCode())
                .likeIfPresent(VcAccountDO::getAccountName, reqVO.getAccountName())
                .eqIfPresent(VcAccountDO::getAccountType, reqVO.getAccountType())
                .likeIfPresent(VcAccountDO::getCompanyName, reqVO.getCompanyName())
                .eqIfPresent(VcAccountDO::getManagerUserId, reqVO.getManagerUserId())
                .likeIfPresent(VcAccountDO::getManagerUserName, reqVO.getManagerUserName())
                .eqIfPresent(VcAccountDO::getStatus, reqVO.getStatus())
                .eqIfPresent(VcAccountDO::getPriorityLevel, reqVO.getPriorityLevel())
                .eqIfPresent(VcAccountDO::getRegionCode, reqVO.getRegionCode())
                .betweenIfPresent(VcAccountDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(VcAccountDO::getId));
    }

    default VcAccountDO selectByAccountCode(String accountCode) {
        return selectOne(VcAccountDO::getAccountCode, accountCode);
    }

    default List<VcAccountDO> selectListByManagerUserId(Long managerUserId) {
        return selectList(VcAccountDO::getManagerUserId, managerUserId);
    }

    default List<VcAccountDO> selectListByStatus(Integer status) {
        return selectList(VcAccountDO::getStatus, status);
    }

    /**
     * 查询用户负责的VC账号列表（包括主负责人和备用负责人）
     */
    @Select("SELECT * FROM vc_account WHERE (manager_user_id = #{userId} OR backup_manager_user_id = #{userId}) AND deleted = 0 AND tenant_id = #{tenantId}")
    List<VcAccountDO> selectListByUserId(@Param("userId") Long userId, @Param("tenantId") Long tenantId);

    /**
     * 统计VC账号的资质数量
     */
    @Select("SELECT COUNT(*) FROM qualification_summary WHERE vc_account_id = #{vcAccountId} AND deleted = 0")
    Long countQualificationsByVcAccountId(@Param("vcAccountId") Long vcAccountId);

    /**
     * 统计VC账号即将过期的资质数量（30天内过期）
     */
    @Select("SELECT COUNT(*) FROM qualification_summary WHERE vc_account_id = #{vcAccountId} AND deleted = 0 AND qualification_expire_date BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL 30 DAY)")
    Long countExpiringQualificationsByVcAccountId(@Param("vcAccountId") Long vcAccountId);

}
