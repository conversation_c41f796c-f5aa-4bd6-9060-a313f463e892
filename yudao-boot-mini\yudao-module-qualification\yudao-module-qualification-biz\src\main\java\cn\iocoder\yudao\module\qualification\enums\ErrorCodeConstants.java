package cn.iocoder.yudao.module.qualification.enums;

import cn.iocoder.yudao.framework.common.exception.ErrorCode;

/**
 * Qualification 错误码枚举类
 *
 * qualification 系统，使用 1-011-000-000 段
 */
public interface ErrorCodeConstants {

    // ========== 资质汇总 ********** ==========
    ErrorCode QUALIFICATION_SUMMARY_NOT_EXISTS = new ErrorCode(**********, "资质汇总不存在");

    // ========== VC账号管理 ********** ==========
    ErrorCode VC_ACCOUNT_NOT_EXISTS = new ErrorCode(**********, "VC账号不存在");
    ErrorCode VC_ACCOUNT_CODE_DUPLICATE = new ErrorCode(**********, "VC账号编码已存在");
    ErrorCode VC_ACCOUNT_HAS_QUALIFICATIONS = new ErrorCode(**********, "VC账号下存在资质数据，无法删除");
    ErrorCode VC_ACCOUNT_MANAGER_NOT_EXISTS = new ErrorCode(**********, "负责人用户不存在");

}