package cn.iocoder.yudao.module.qualification.service;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.qualification.controller.admin.vcaccount.vo.VcAccountPageReqVO;
import cn.iocoder.yudao.module.qualification.controller.admin.vcaccount.vo.VcAccountSaveReqVO;
import cn.iocoder.yudao.module.qualification.dal.dataobject.VcAccountDO;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;

/**
 * VC账号管理 Service 接口
 *
 * <AUTHOR>
 */
public interface VcAccountService {

    /**
     * 创建VC账号
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createVcAccount(@Valid VcAccountSaveReqVO createReqVO);

    /**
     * 更新VC账号
     *
     * @param updateReqVO 更新信息
     */
    void updateVcAccount(@Valid VcAccountSaveReqVO updateReqVO);

    /**
     * 删除VC账号
     *
     * @param id 编号
     */
    void deleteVcAccount(Long id);

    /**
     * 获得VC账号
     *
     * @param id 编号
     * @return VC账号
     */
    VcAccountDO getVcAccount(Long id);

    /**
     * 获得VC账号列表
     *
     * @param ids 编号
     * @return VC账号列表
     */
    List<VcAccountDO> getVcAccountList(Collection<Long> ids);

    /**
     * 获得VC账号分页
     *
     * @param pageReqVO 分页查询
     * @return VC账号分页
     */
    PageResult<VcAccountDO> getVcAccountPage(VcAccountPageReqVO pageReqVO);

    /**
     * 获得VC账号列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return VC账号列表
     */
    List<VcAccountDO> getVcAccountList(VcAccountPageReqVO exportReqVO);

    /**
     * 根据账号编码获取VC账号
     *
     * @param accountCode 账号编码
     * @return VC账号
     */
    VcAccountDO getVcAccountByCode(String accountCode);

    /**
     * 获取用户负责的VC账号列表
     *
     * @param userId 用户ID
     * @return VC账号列表
     */
    List<VcAccountDO> getVcAccountListByUserId(Long userId);

    /**
     * 获取简单的VC账号列表（用于下拉选择）
     *
     * @return VC账号列表
     */
    List<VcAccountDO> getSimpleVcAccountList();

    /**
     * 校验VC账号编码的唯一性
     *
     * @param id 编号
     * @param accountCode 账号编码
     */
    void validateAccountCodeUnique(Long id, String accountCode);

    /**
     * 校验VC账号是否存在
     *
     * @param id 编号
     */
    void validateVcAccountExists(Long id);

}
