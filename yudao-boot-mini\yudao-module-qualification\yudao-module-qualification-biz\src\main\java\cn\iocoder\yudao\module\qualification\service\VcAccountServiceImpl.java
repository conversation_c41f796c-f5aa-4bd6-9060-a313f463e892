package cn.iocoder.yudao.module.qualification.service;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.qualification.controller.admin.vcaccount.vo.VcAccountPageReqVO;
import cn.iocoder.yudao.module.qualification.controller.admin.vcaccount.vo.VcAccountSaveReqVO;
import cn.iocoder.yudao.module.qualification.dal.dataobject.VcAccountDO;
import cn.iocoder.yudao.module.qualification.dal.mysql.VcAccountMapper;
import cn.iocoder.yudao.module.system.api.user.AdminUserApi;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserRespDTO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.Objects;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder.getTenantId;
import static cn.iocoder.yudao.module.qualification.enums.ErrorCodeConstants.*;

/**
 * VC账号管理 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class VcAccountServiceImpl implements VcAccountService {

    @Resource
    private VcAccountMapper vcAccountMapper;

    @Resource
    private AdminUserApi adminUserApi;

    @Override
    public Long createVcAccount(VcAccountSaveReqVO createReqVO) {
        // 校验账号编码唯一性
        validateAccountCodeUnique(null, createReqVO.getAccountCode());
        
        // 校验负责人是否存在
        validateManagerUser(createReqVO.getManagerUserId());
        if (createReqVO.getBackupManagerUserId() != null) {
            validateManagerUser(createReqVO.getBackupManagerUserId());
        }

        // 插入
        VcAccountDO vcAccount = BeanUtils.toBean(createReqVO, VcAccountDO.class);
        // 设置负责人姓名
        setManagerUserName(vcAccount);
        vcAccountMapper.insert(vcAccount);
        
        // 返回
        return vcAccount.getId();
    }

    @Override
    public void updateVcAccount(VcAccountSaveReqVO updateReqVO) {
        // 校验存在
        validateVcAccountExists(updateReqVO.getId());
        // 校验账号编码唯一性
        validateAccountCodeUnique(updateReqVO.getId(), updateReqVO.getAccountCode());
        
        // 校验负责人是否存在
        validateManagerUser(updateReqVO.getManagerUserId());
        if (updateReqVO.getBackupManagerUserId() != null) {
            validateManagerUser(updateReqVO.getBackupManagerUserId());
        }

        // 更新
        VcAccountDO updateObj = BeanUtils.toBean(updateReqVO, VcAccountDO.class);
        // 设置负责人姓名
        setManagerUserName(updateObj);
        vcAccountMapper.updateById(updateObj);
    }

    @Override
    public void deleteVcAccount(Long id) {
        // 校验存在
        validateVcAccountExists(id);
        
        // 校验是否有关联的资质数据
        Long qualificationCount = vcAccountMapper.countQualificationsByVcAccountId(id);
        if (qualificationCount > 0) {
            throw exception(VC_ACCOUNT_HAS_QUALIFICATIONS);
        }
        
        // 删除
        vcAccountMapper.deleteById(id);
    }

    @Override
    public VcAccountDO getVcAccount(Long id) {
        return vcAccountMapper.selectById(id);
    }

    @Override
    public List<VcAccountDO> getVcAccountList(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return CollUtil.newArrayList();
        }
        return vcAccountMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<VcAccountDO> getVcAccountPage(VcAccountPageReqVO pageReqVO) {
        return vcAccountMapper.selectPage(pageReqVO);
    }

    @Override
    public List<VcAccountDO> getVcAccountList(VcAccountPageReqVO exportReqVO) {
        return vcAccountMapper.selectList(exportReqVO);
    }

    @Override
    public VcAccountDO getVcAccountByCode(String accountCode) {
        return vcAccountMapper.selectByAccountCode(accountCode);
    }

    @Override
    public List<VcAccountDO> getVcAccountListByUserId(Long userId) {
        return vcAccountMapper.selectListByUserId(userId, getTenantId());
    }

    @Override
    public List<VcAccountDO> getSimpleVcAccountList() {
        return vcAccountMapper.selectListByStatus(1); // 只返回正常状态的账号
    }

    @Override
    public void validateAccountCodeUnique(Long id, String accountCode) {
        VcAccountDO vcAccount = vcAccountMapper.selectByAccountCode(accountCode);
        if (vcAccount == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同 id 的账号
        if (id == null) {
            throw exception(VC_ACCOUNT_CODE_DUPLICATE);
        }
        if (!Objects.equals(vcAccount.getId(), id)) {
            throw exception(VC_ACCOUNT_CODE_DUPLICATE);
        }
    }

    @Override
    public void validateVcAccountExists(Long id) {
        if (vcAccountMapper.selectById(id) == null) {
            throw exception(VC_ACCOUNT_NOT_EXISTS);
        }
    }

    /**
     * 校验负责人用户是否存在
     */
    private void validateManagerUser(Long userId) {
        AdminUserRespDTO user = adminUserApi.getUser(userId);
        if (user == null) {
            throw exception(VC_ACCOUNT_MANAGER_NOT_EXISTS);
        }
    }

    /**
     * 设置负责人姓名
     */
    private void setManagerUserName(VcAccountDO vcAccount) {
        if (vcAccount.getManagerUserId() != null) {
            AdminUserRespDTO user = adminUserApi.getUser(vcAccount.getManagerUserId());
            if (user != null) {
                vcAccount.setManagerUserName(user.getNickname());
            }
        }
        if (vcAccount.getBackupManagerUserId() != null) {
            AdminUserRespDTO user = adminUserApi.getUser(vcAccount.getBackupManagerUserId());
            if (user != null) {
                vcAccount.setBackupManagerUserName(user.getNickname());
            }
        }
    }

}
