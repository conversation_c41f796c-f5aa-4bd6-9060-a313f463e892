package cn.iocoder.yudao.module.qualification.convert;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.qualification.controller.admin.summary.vo.QualificationSummaryCreateReqVO;
import cn.iocoder.yudao.module.qualification.controller.admin.summary.vo.QualificationSummaryExcelVO;
import cn.iocoder.yudao.module.qualification.controller.admin.summary.vo.QualificationSummaryRespVO;
import cn.iocoder.yudao.module.qualification.controller.admin.summary.vo.QualificationSummaryUpdateReqVO;
import cn.iocoder.yudao.module.qualification.dal.dataobject.QualificationSummaryDO;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-05T09:44:44+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 1.8.0_452 (Amazon.com Inc.)"
)
public class QualificationSummaryConvertImpl implements QualificationSummaryConvert {

    @Override
    public QualificationSummaryDO convert(QualificationSummaryCreateReqVO bean) {
        if ( bean == null ) {
            return null;
        }

        QualificationSummaryDO.QualificationSummaryDOBuilder qualificationSummaryDO = QualificationSummaryDO.builder();

        qualificationSummaryDO.vcAccount( bean.getVcAccount() );
        qualificationSummaryDO.brand( bean.getBrand() );
        qualificationSummaryDO.brandCode( bean.getBrandCode() );
        qualificationSummaryDO.firstCategory( bean.getFirstCategory() );
        qualificationSummaryDO.firstCategoryCode( bean.getFirstCategoryCode() );
        qualificationSummaryDO.secondCategory( bean.getSecondCategory() );
        qualificationSummaryDO.secondCategoryCode( bean.getSecondCategoryCode() );
        qualificationSummaryDO.thirdCategory( bean.getThirdCategory() );
        qualificationSummaryDO.thirdCategoryCode( bean.getThirdCategoryCode() );
        qualificationSummaryDO.productLineType( bean.getProductLineType() );
        qualificationSummaryDO.firstDepartment( bean.getFirstDepartment() );
        qualificationSummaryDO.firstDepartmentCode( bean.getFirstDepartmentCode() );
        qualificationSummaryDO.purchaser( bean.getPurchaser() );
        qualificationSummaryDO.purchaserName( bean.getPurchaserName() );
        qualificationSummaryDO.productLineLevel( bean.getProductLineLevel() );
        qualificationSummaryDO.qualificationExpireDate( bean.getQualificationExpireDate() );
        qualificationSummaryDO.status( bean.getStatus() );
        qualificationSummaryDO.expiredStatus( bean.getExpiredStatus() );
        qualificationSummaryDO.remark( bean.getRemark() );

        return qualificationSummaryDO.build();
    }

    @Override
    public QualificationSummaryDO convert(QualificationSummaryUpdateReqVO bean) {
        if ( bean == null ) {
            return null;
        }

        QualificationSummaryDO.QualificationSummaryDOBuilder qualificationSummaryDO = QualificationSummaryDO.builder();

        qualificationSummaryDO.id( bean.getId() );
        qualificationSummaryDO.vcAccount( bean.getVcAccount() );
        qualificationSummaryDO.brand( bean.getBrand() );
        qualificationSummaryDO.brandCode( bean.getBrandCode() );
        qualificationSummaryDO.firstCategory( bean.getFirstCategory() );
        qualificationSummaryDO.firstCategoryCode( bean.getFirstCategoryCode() );
        qualificationSummaryDO.secondCategory( bean.getSecondCategory() );
        qualificationSummaryDO.secondCategoryCode( bean.getSecondCategoryCode() );
        qualificationSummaryDO.thirdCategory( bean.getThirdCategory() );
        qualificationSummaryDO.thirdCategoryCode( bean.getThirdCategoryCode() );
        qualificationSummaryDO.productLineType( bean.getProductLineType() );
        qualificationSummaryDO.firstDepartment( bean.getFirstDepartment() );
        qualificationSummaryDO.firstDepartmentCode( bean.getFirstDepartmentCode() );
        qualificationSummaryDO.purchaser( bean.getPurchaser() );
        qualificationSummaryDO.purchaserName( bean.getPurchaserName() );
        qualificationSummaryDO.productLineLevel( bean.getProductLineLevel() );
        qualificationSummaryDO.qualificationExpireDate( bean.getQualificationExpireDate() );
        qualificationSummaryDO.status( bean.getStatus() );
        qualificationSummaryDO.expiredStatus( bean.getExpiredStatus() );
        qualificationSummaryDO.remark( bean.getRemark() );

        return qualificationSummaryDO.build();
    }

    @Override
    public QualificationSummaryRespVO convert(QualificationSummaryDO bean) {
        if ( bean == null ) {
            return null;
        }

        QualificationSummaryRespVO qualificationSummaryRespVO = new QualificationSummaryRespVO();

        qualificationSummaryRespVO.setVcAccount( bean.getVcAccount() );
        qualificationSummaryRespVO.setBrand( bean.getBrand() );
        qualificationSummaryRespVO.setBrandCode( bean.getBrandCode() );
        qualificationSummaryRespVO.setFirstCategory( bean.getFirstCategory() );
        qualificationSummaryRespVO.setFirstCategoryCode( bean.getFirstCategoryCode() );
        qualificationSummaryRespVO.setSecondCategory( bean.getSecondCategory() );
        qualificationSummaryRespVO.setSecondCategoryCode( bean.getSecondCategoryCode() );
        qualificationSummaryRespVO.setThirdCategory( bean.getThirdCategory() );
        qualificationSummaryRespVO.setThirdCategoryCode( bean.getThirdCategoryCode() );
        qualificationSummaryRespVO.setProductLineType( bean.getProductLineType() );
        qualificationSummaryRespVO.setFirstDepartment( bean.getFirstDepartment() );
        qualificationSummaryRespVO.setFirstDepartmentCode( bean.getFirstDepartmentCode() );
        qualificationSummaryRespVO.setPurchaser( bean.getPurchaser() );
        qualificationSummaryRespVO.setPurchaserName( bean.getPurchaserName() );
        qualificationSummaryRespVO.setProductLineLevel( bean.getProductLineLevel() );
        qualificationSummaryRespVO.setQualificationExpireDate( bean.getQualificationExpireDate() );
        qualificationSummaryRespVO.setStatus( bean.getStatus() );
        qualificationSummaryRespVO.setExpiredStatus( bean.getExpiredStatus() );
        qualificationSummaryRespVO.setRemark( bean.getRemark() );
        qualificationSummaryRespVO.setId( bean.getId() );
        qualificationSummaryRespVO.setCreateTime( bean.getCreateTime() );

        return qualificationSummaryRespVO;
    }

    @Override
    public List<QualificationSummaryRespVO> convertList(List<QualificationSummaryDO> list) {
        if ( list == null ) {
            return null;
        }

        List<QualificationSummaryRespVO> list1 = new ArrayList<QualificationSummaryRespVO>( list.size() );
        for ( QualificationSummaryDO qualificationSummaryDO : list ) {
            list1.add( convert( qualificationSummaryDO ) );
        }

        return list1;
    }

    @Override
    public PageResult<QualificationSummaryRespVO> convertPage(PageResult<QualificationSummaryDO> page) {
        if ( page == null ) {
            return null;
        }

        PageResult<QualificationSummaryRespVO> pageResult = new PageResult<QualificationSummaryRespVO>();

        pageResult.setList( convertList( page.getList() ) );
        pageResult.setTotal( page.getTotal() );

        return pageResult;
    }

    @Override
    public QualificationSummaryExcelVO convertToExcel(QualificationSummaryDO bean) {
        if ( bean == null ) {
            return null;
        }

        QualificationSummaryExcelVO qualificationSummaryExcelVO = new QualificationSummaryExcelVO();

        qualificationSummaryExcelVO.setExpiredStatusText( convertExpiredStatus( bean.getExpiredStatus() ) );
        qualificationSummaryExcelVO.setVcAccount( bean.getVcAccount() );
        qualificationSummaryExcelVO.setBrand( bean.getBrand() );
        qualificationSummaryExcelVO.setFirstCategory( bean.getFirstCategory() );
        qualificationSummaryExcelVO.setSecondCategory( bean.getSecondCategory() );
        qualificationSummaryExcelVO.setThirdCategory( bean.getThirdCategory() );
        qualificationSummaryExcelVO.setProductLineType( bean.getProductLineType() );
        qualificationSummaryExcelVO.setFirstDepartment( bean.getFirstDepartment() );
        qualificationSummaryExcelVO.setPurchaser( bean.getPurchaser() );
        qualificationSummaryExcelVO.setPurchaserName( bean.getPurchaserName() );
        qualificationSummaryExcelVO.setProductLineLevel( bean.getProductLineLevel() );
        if ( bean.getQualificationExpireDate() != null ) {
            qualificationSummaryExcelVO.setQualificationExpireDate( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( bean.getQualificationExpireDate() ) );
        }
        qualificationSummaryExcelVO.setStatus( bean.getStatus() );
        qualificationSummaryExcelVO.setRemark( bean.getRemark() );
        if ( bean.getCreateTime() != null ) {
            qualificationSummaryExcelVO.setCreateTime( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( bean.getCreateTime() ) );
        }

        return qualificationSummaryExcelVO;
    }
}
