// VC账号管理相关类型定义和API接口
// 注意：在当前项目中，API调用主要通过 requestClient 在组件中直接调用
// 这个文件主要用于类型定义和常量

export interface VcAccount {
  id?: number
  accountCode: string
  accountName: string
  accountType?: string
  companyName?: string
  contactPerson?: string
  contactPhone?: string
  contactEmail?: string
  businessScope?: string
  regionCode?: string
  regionName?: string
  managerUserId: number
  managerUserName?: string
  backupManagerUserId?: number
  backupManagerUserName?: string
  status: number
  priorityLevel?: number
  remark?: string
  createTime?: string
  updateTime?: string
  qualificationCount?: number
  expiringCount?: number
}

export interface VcAccountPageReq {
  pageNo?: number
  pageSize?: number
  accountCode?: string
  accountName?: string
  accountType?: string
  companyName?: string
  managerUserId?: number
  managerUserName?: string
  status?: number
  priorityLevel?: number
  regionCode?: string
  createTime?: [string, string]
}

export interface VcAccountCreateReq {
  accountCode: string
  accountName: string
  accountType?: string
  companyName?: string
  contactPerson?: string
  contactPhone?: string
  contactEmail?: string
  businessScope?: string
  regionCode?: string
  regionName?: string
  managerUserId: number
  backupManagerUserId?: number
  status: number
  priorityLevel?: number
  remark?: string
}

export interface VcAccountUpdateReq extends VcAccountCreateReq {
  id: number
}

// VC账号状态选项
export const VC_ACCOUNT_STATUS_OPTIONS = [
  { label: '正常', value: 1 },
  { label: '停用', value: 0 }
]

// VC账号类型选项
export const VC_ACCOUNT_TYPE_OPTIONS = [
  { label: '旗舰店', value: '旗舰店' },
  { label: '专营店', value: '专营店' },
  { label: '代理商', value: '代理商' },
  { label: '直营店', value: '直营店' },
  { label: '加盟店', value: '加盟店' }
]

// 优先级选项
export const PRIORITY_LEVEL_OPTIONS = [
  { label: '高', value: 1 },
  { label: '中', value: 2 },
  { label: '低', value: 3 }
]
