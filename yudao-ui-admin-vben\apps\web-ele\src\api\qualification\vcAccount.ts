import { defHttp } from '@/utils/http/axios'

// 定义通用类型
interface PageParam {
  pageNo?: number
  pageSize?: number
}

interface PageResult<T> {
  list: T[]
  total: number
}

export interface VcAccountVO {
  id?: number
  accountCode: string
  accountName: string
  accountType?: string
  companyName?: string
  contactPerson?: string
  contactPhone?: string
  contactEmail?: string
  businessScope?: string
  regionCode?: string
  regionName?: string
  managerUserId: number
  managerUserName?: string
  backupManagerUserId?: number
  backupManagerUserName?: string
  status: number
  priorityLevel?: number
  remark?: string
  createTime?: Date
  updateTime?: Date
  creator?: string
  updater?: string
  qualificationCount?: number
  expiringCount?: number
}

export interface VcAccountPageReqVO extends PageParam {
  accountCode?: string
  accountName?: string
  accountType?: string
  companyName?: string
  managerUserId?: number
  managerUserName?: string
  status?: number
  priorityLevel?: number
  regionCode?: string
  createTime?: [string, string]
}

// 查询VC账号分页
export const getVcAccountPage = (params: VcAccountPageReqVO) => {
  return defHttp.get<PageResult<VcAccountVO>>({ url: '/qualification/vc-account/page', params })
}

// 查询VC账号详情
export const getVcAccount = (id: number) => {
  return defHttp.get<VcAccountVO>({ url: '/qualification/vc-account/get', params: { id } })
}

// 新增VC账号
export const createVcAccount = (data: VcAccountVO) => {
  return defHttp.post<number>({ url: '/qualification/vc-account/create', data })
}

// 修改VC账号
export const updateVcAccount = (data: VcAccountVO) => {
  return defHttp.put<boolean>({ url: '/qualification/vc-account/update', data })
}

// 删除VC账号
export const deleteVcAccount = (id: number) => {
  return defHttp.delete<boolean>({ url: '/qualification/vc-account/delete', params: { id } })
}

// 导出VC账号 Excel
export const exportVcAccount = (params: VcAccountPageReqVO) => {
  return defHttp.download({ url: '/qualification/vc-account/export-excel', params })
}

// 获取VC账号简单列表
export const getSimpleVcAccountList = () => {
  return defHttp.get<VcAccountVO[]>({ url: '/qualification/vc-account/simple-list' })
}

// 获取我负责的VC账号列表
export const getMyManagedAccounts = () => {
  return defHttp.get<VcAccountVO[]>({ url: '/qualification/vc-account/my-managed-accounts' })
}

// VC账号状态选项
export const VC_ACCOUNT_STATUS_OPTIONS = [
  { label: '正常', value: 1, color: 'success' },
  { label: '停用', value: 0, color: 'error' }
]

// VC账号类型选项
export const VC_ACCOUNT_TYPE_OPTIONS = [
  { label: '旗舰店', value: '旗舰店' },
  { label: '专营店', value: '专营店' },
  { label: '代理商', value: '代理商' },
  { label: '直营店', value: '直营店' },
  { label: '加盟店', value: '加盟店' }
]

// 优先级选项
export const PRIORITY_LEVEL_OPTIONS = [
  { label: '高', value: 1, color: 'error' },
  { label: '中', value: 2, color: 'warning' },
  { label: '低', value: 3, color: 'default' }
]
