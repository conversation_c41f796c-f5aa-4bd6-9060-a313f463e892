<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="getTitle" @ok="handleSubmit">
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, computed, unref } from 'vue'
import { BasicModal, useModalInner } from '@/components/Modal'
import { BasicForm, useForm } from '@/components/Form'
import { formSchema } from './vcAccount.data'
import { createVcAccount, updateVcAccount, getVcAccount } from '@/api/qualification/vcAccount'
import { useMessage } from '@/hooks/web/useMessage'

defineOptions({ name: 'VcAccountModal' })

const emit = defineEmits(['success', 'register'])
const { createMessage } = useMessage()

const isUpdate = ref(true)
const rowId = ref<number>()

const [registerForm, { setFieldsValue, resetFields, validate }] = useForm({
  labelWidth: 120,
  baseColProps: { span: 24 },
  schemas: formSchema,
  showActionButtonGroup: false,
  autoSubmitOnEnter: true,
})

const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
  resetFields()
  setModalProps({ confirmLoading: false })
  
  isUpdate.value = !!data?.isUpdate

  if (unref(isUpdate)) {
    rowId.value = data.record.id
    // 加载详细数据
    const vcAccount = await getVcAccount(data.record.id)
    setFieldsValue({
      ...vcAccount,
    })
  }
})

const getTitle = computed(() => (!unref(isUpdate) ? '新增VC账号' : '编辑VC账号'))

async function handleSubmit() {
  try {
    const values = await validate()
    setModalProps({ confirmLoading: true })
    
    if (unref(isUpdate)) {
      await updateVcAccount({ ...values, id: rowId.value })
      createMessage.success('修改成功')
    } else {
      await createVcAccount(values)
      createMessage.success('新增成功')
    }
    
    closeModal()
    emit('success', { isUpdate: unref(isUpdate), values: { ...values, id: rowId.value } })
  } finally {
    setModalProps({ confirmLoading: false })
  }
}
</script>
