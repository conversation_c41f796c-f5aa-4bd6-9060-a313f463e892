<template>
  <div>
    <!-- 搜索表单 -->
    <BasicForm @register="registerForm" @submit="handleQuery" @reset="handleQuery" />
    
    <!-- 操作工具栏 -->
    <BasicTable @register="registerTable" @fetch-success="onFetchSuccess">
      <template #toolbar>
        <a-button
          v-auth="['vc:account:create']"
          type="primary"
          :preIcon="IconEnum.ADD"
          @click="handleCreate"
        >
          新增VC账号
        </a-button>
        <a-button
          v-auth="['vc:account:export']"
          :preIcon="IconEnum.EXPORT"
          @click="handleExport"
        >
          导出
        </a-button>
      </template>
      
      <!-- 表格列操作 -->
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'status'">
          <dict-tag :type="DICT_TYPE.COMMON_STATUS" :value="record.status" />
        </template>
        <template v-else-if="column.key === 'priorityLevel'">
          <a-tag v-if="record.priorityLevel" :color="getPriorityColor(record.priorityLevel)">
            {{ getPriorityText(record.priorityLevel) }}
          </a-tag>
        </template>
        <template v-else-if="column.key === 'qualificationCount'">
          <a-button type="link" size="small" @click="handleViewQualifications(record)">
            {{ record.qualificationCount || 0 }}
          </a-button>
        </template>
        <template v-else-if="column.key === 'expiringCount'">
          <a-tag v-if="record.expiringCount > 0" color="warning">
            {{ record.expiringCount }}
          </a-tag>
          <span v-else>{{ record.expiringCount || 0 }}</span>
        </template>
        <template v-else-if="column.key === 'action'">
          <TableAction
            :actions="[
              {
                icon: IconEnum.EDIT,
                label: '修改',
                auth: 'vc:account:update',
                onClick: handleEdit.bind(null, record),
              },
              {
                icon: IconEnum.DELETE,
                color: 'error',
                label: '删除',
                auth: 'vc:account:delete',
                popConfirm: {
                  title: '是否确认删除',
                  placement: 'left',
                  confirm: handleDelete.bind(null, record),
                },
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    
    <!-- 表单弹窗：添加/修改 -->
    <VcAccountModal @register="registerModal" @success="reload" />
  </div>
</template>

<script lang="ts" setup>
import { reactive } from 'vue'
import { BasicForm, useForm } from '@/components/Form'
import { BasicTable, useTable, TableAction } from '@/components/Table'
import { useModal } from '@/components/Modal'
import { useMessage } from '@/hooks/web/useMessage'
import { IconEnum } from '@/enums/appEnum'
import { DICT_TYPE, getDictOptions } from '@/utils/dict'

import { columns, searchFormSchema } from './vcAccount.data'
import { 
  getVcAccountPage, 
  deleteVcAccount, 
  exportVcAccount,
  PRIORITY_LEVEL_OPTIONS 
} from '@/api/qualification/vcAccount'
import VcAccountModal from './VcAccountModal.vue'

defineOptions({ name: 'VcAccount' })

const { createMessage } = useMessage()
const [registerModal, { openModal }] = useModal()

// 搜索表单
const [registerForm, { getFieldsValue, resetFields }] = useForm({
  labelWidth: 100,
  schemas: searchFormSchema,
  autoSubmitOnEnter: true,
  submitFunc: handleQuery,
  resetFunc: handleQuery,
  actionColOptions: {
    span: 24,
  },
})

// 表格
const [registerTable, { reload, getSelectRows }] = useTable({
  title: 'VC账号列表',
  api: getVcAccountPage,
  columns,
  formConfig: {
    labelWidth: 120,
    schemas: searchFormSchema,
  },
  useSearchForm: false, // 使用独立的搜索表单
  showTableSetting: true,
  tableSetting: { fullScreen: true },
  actionColumn: {
    width: 140,
    title: '操作',
    dataIndex: 'action',
    fixed: undefined,
  },
})

// 搜索
function handleQuery() {
  const values = getFieldsValue()
  reload({ searchInfo: values })
}

// 新增
function handleCreate() {
  openModal(true, { isUpdate: false })
}

// 修改
function handleEdit(record: Recordable) {
  openModal(true, { record, isUpdate: true })
}

// 删除
async function handleDelete(record: Recordable) {
  await deleteVcAccount(record.id)
  createMessage.success('删除成功')
  reload()
}

// 导出
async function handleExport() {
  const values = getFieldsValue()
  await exportVcAccount(values)
}

// 查看资质
function handleViewQualifications(record: Recordable) {
  // 跳转到资质管理页面，并筛选该VC账号的资质
  // 这里可以使用路由跳转或者打开新的弹窗
  console.log('查看VC账号资质:', record)
}

// 获取优先级颜色
function getPriorityColor(level: number) {
  const option = PRIORITY_LEVEL_OPTIONS.find(item => item.value === level)
  return option?.color || 'default'
}

// 获取优先级文本
function getPriorityText(level: number) {
  const option = PRIORITY_LEVEL_OPTIONS.find(item => item.value === level)
  return option?.label || '未知'
}

// 表格数据加载成功回调
function onFetchSuccess() {
  // 可以在这里处理一些额外的逻辑
}
</script>
