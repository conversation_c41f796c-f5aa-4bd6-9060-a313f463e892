<template>
  <Page auto-content-height>
    <FormModal @success="onRefresh" />

    <Grid table-title="VC账号管理列表">
      <template #toolbar-tools>
        <TableAction
          :actions="[
            {
              label: $t('ui.actionTitle.create', ['VC账号']),
              type: 'primary',
              icon: ACTION_ICON.ADD,
              auth: ['vc:account:create'],
              onClick: onCreate,
            },
            {
              label: $t('ui.actionTitle.export'),
              type: 'primary',
              icon: ACTION_ICON.DOWNLOAD,
              auth: ['vc:account:export'],
              onClick: onExport,
            },
          ]"
        />
      </template>
      <template #actions="{ row }">
        <TableAction
          :actions="[
            {
              label: $t('common.edit'),
              type: 'text',
              icon: ACTION_ICON.EDIT,
              auth: ['vc:account:update'],
              onClick: onEdit.bind(null, row),
            },
            {
              label: $t('common.delete'),
              type: 'danger',
              text: true,
              icon: ACTION_ICON.DELETE,
              auth: ['vc:account:delete'],
              popConfirm: {
                title: $t('ui.actionMessage.deleteConfirm', [row.accountName]),
                confirm: onDelete.bind(null, row),
              },
            },
          ]"
        />
      </template>
    </Grid>
  </Page>
</template>

<script lang="ts" setup>
import type { VxeTableGridOptions } from '#/adapter/vxe-table'

import { Page, useVbenModal } from '@vben/common-ui'
import { downloadFileFromBlobPart } from '@vben/utils'

import { ElLoading, ElMessage } from 'element-plus'

import { ACTION_ICON, TableAction, useVbenVxeGrid } from '#/adapter/vxe-table'
import { $t } from '#/locales'

import { requestClient } from '#/api/request'

import { useGridColumns, useGridFormSchema } from './vcAccount.data'
import Form from './modules/form.vue'

// 定义接口类型
interface VcAccount {
  id?: number
  accountCode: string
  accountName: string
  accountType?: string
  companyName?: string
  contactPerson?: string
  contactPhone?: string
  contactEmail?: string
  businessScope?: string
  regionCode?: string
  regionName?: string
  managerUserId: number
  managerUserName?: string
  backupManagerUserId?: number
  backupManagerUserName?: string
  status: number
  priorityLevel?: number
  remark?: string
  createTime?: string
  updateTime?: string
  qualificationCount?: number
  expiringCount?: number
}

// API 函数
const getVcAccountPage = async (params: any) => {
  return await requestClient.get('/qualification/vc-account/page', { params })
}

const deleteVcAccount = async (id: number) => {
  return await requestClient.delete(`/qualification/vc-account/delete?id=${id}`)
}

const exportVcAccount = async (params: any) => {
  const response = await requestClient.get('/qualification/vc-account/export-excel', {
    params,
    responseType: 'blob'
  })
  return response
}

defineOptions({ name: 'VcAccount' })

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: Form,
})

/** 新增VC账号 */
function onCreate() {
  formModalApi.open()
}

/** 编辑VC账号 */
function onEdit(row: VcAccount) {
  formModalApi.open({ vcAccountData: row })
}

/** 删除VC账号 */
async function onDelete(row: VcAccount) {
  const loadingInstance = ElLoading.service({
    fullscreen: true,
  })
  try {
    await deleteVcAccount(row.id as number)
    ElMessage.success($t('ui.actionMessage.deleteSuccess', [row.accountName]))
    onRefresh()
  } catch {
    // 异常处理
  } finally {
    loadingInstance.close()
  }
}

/** 刷新表格 */
function onRefresh() {
  gridApi.query()
}

/** 导出表格 */
async function onExport() {
  const data = await exportVcAccount(await gridApi.formApi.getValues())
  downloadFileFromBlobPart({ fileName: 'VC账号管理.xlsx', source: data })
}

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    schema: useGridFormSchema(),
  },
  gridOptions: {
    columns: useGridColumns(),
    height: 'auto',
    keepSource: true,
    proxyConfig: {
      ajax: {
        query: async ({ page }, formValues) => {
          return await getVcAccountPage({
            pageNo: page.currentPage,
            pageSize: page.pageSize,
            ...formValues,
          })
        },
      },
    },
    rowConfig: {
      keyField: 'id',
    },
    toolbarConfig: {
      refresh: { code: 'query' },
      search: true,
    },
  } as VxeTableGridOptions<VcAccount>,
})
</script>
