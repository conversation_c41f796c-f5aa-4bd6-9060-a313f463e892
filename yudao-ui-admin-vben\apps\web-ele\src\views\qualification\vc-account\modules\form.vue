<script lang="ts" setup>
import { computed, watch } from 'vue'

import { useVbenModal } from '@vben/common-ui'

import { ElMessage } from 'element-plus'

import { useVbenForm } from '#/adapter/form'
import { requestClient } from '#/api/request'
import { $t } from '#/locales'

import { useFormSchema } from '../vcAccount.data'

interface Props {
  vcAccountData?: any
}

const props = withDefaults(defineProps<Props>(), {
  vcAccountData: () => ({}),
})

const emit = defineEmits<{
  success: []
}>()

const [Form, formApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
    formItemClass: 'col-span-2',
    labelWidth: 120,
  },
  layout: 'horizontal',
  schema: useFormSchema(),
  showDefaultActions: false,
})

const [Modal, modalApi] = useVbenModal({
  async onConfirm() {
    const { valid } = await formApi.validate()
    if (!valid) {
      return
    }
    modalApi.lock()
    // 提交表单
    const data = await formApi.getValues()
    try {
      if (formData.value?.id) {
        await requestClient.put('/qualification/vc-account/update', {
          ...data,
          id: formData.value.id,
        })
        ElMessage.success('修改成功')
      } else {
        await requestClient.post('/qualification/vc-account/create', data)
        ElMessage.success('新增成功')
      }
      modalApi.close()
      emit('success')
    } catch (error) {
      console.error('提交失败:', error)
    } finally {
      modalApi.unlock()
    }
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      const vcAccountData = props.vcAccountData
      if (vcAccountData?.id) {
        // 编辑模式
        formApi.setValues(vcAccountData)
      } else {
        // 新增模式
        formApi.resetForm()
      }
    }
  },
})

const formData = computed(() => props.vcAccountData)

const modalTitle = computed(() => {
  return formData.value?.id ? '编辑VC账号' : '新增VC账号'
})

watch(
  () => props.vcAccountData,
  (newData) => {
    if (newData?.id) {
      formApi.setValues(newData)
    }
  },
  { deep: true }
)

defineExpose({
  modalApi,
})
</script>

<template>
  <Modal :title="modalTitle" class="w-[800px]">
    <Form />
  </Modal>
</template>
