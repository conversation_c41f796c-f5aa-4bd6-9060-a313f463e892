import { BasicColumn } from '@/components/Table'
import { FormSchema } from '@/components/Table'
import { DICT_TYPE, getDictOptions } from '@/utils/dict'
import { VC_ACCOUNT_STATUS_OPTIONS, VC_ACCOUNT_TYPE_OPTIONS } from '@/api/qualification/vcAccount'
import { getUserSimpleList } from '@/api/system/user'

// 表格列配置
export const columns: BasicColumn[] = [
  {
    title: 'VC账号编码',
    dataIndex: 'accountCode',
    width: 150,
    fixed: 'left',
  },
  {
    title: 'VC账号名称',
    dataIndex: 'accountName',
    width: 200,
    fixed: 'left',
  },
  {
    title: '账号类型',
    dataIndex: 'accountType',
    width: 120,
  },
  {
    title: '公司名称',
    dataIndex: 'companyName',
    width: 200,
  },
  {
    title: '负责人',
    dataIndex: 'managerUserName',
    width: 120,
  },
  {
    title: '联系电话',
    dataIndex: 'contactPhone',
    width: 130,
  },
  {
    title: '资质数量',
    dataIndex: 'qualificationCount',
    width: 100,
  },
  {
    title: '即将过期',
    dataIndex: 'expiringCount',
    width: 100,
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 80,
  },
  {
    title: '优先级',
    dataIndex: 'priorityLevel',
    width: 80,
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 180,
    sorter: true,
  },
]

// 搜索表单Schema
export const searchFormSchema: FormSchema[] = [
  {
    label: 'VC账号编码',
    field: 'accountCode',
    component: 'Input',
    colProps: { span: 8 },
  },
  {
    label: 'VC账号名称',
    field: 'accountName',
    component: 'Input',
    colProps: { span: 8 },
  },
  {
    label: '账号类型',
    field: 'accountType',
    component: 'Select',
    componentProps: {
      options: VC_ACCOUNT_TYPE_OPTIONS,
      placeholder: '请选择账号类型',
    },
    colProps: { span: 8 },
  },
  {
    label: '负责人',
    field: 'managerUserId',
    component: 'ApiSelect',
    componentProps: {
      api: getUserSimpleList,
      labelField: 'nickname',
      valueField: 'id',
      placeholder: '请选择负责人',
    },
    colProps: { span: 8 },
  },
  {
    label: '状态',
    field: 'status',
    component: 'Select',
    componentProps: {
      options: VC_ACCOUNT_STATUS_OPTIONS,
      placeholder: '请选择状态',
    },
    colProps: { span: 8 },
  },
  {
    label: '创建时间',
    field: 'createTime',
    component: 'RangePicker',
    colProps: { span: 8 },
  },
]

// 表单Schema
export const formSchema: FormSchema[] = [
  {
    label: '编号',
    field: 'id',
    show: false,
    component: 'Input',
  },
  {
    label: 'VC账号编码',
    field: 'accountCode',
    required: true,
    component: 'Input',
    componentProps: {
      placeholder: '请输入VC账号编码',
      maxlength: 100,
    },
    rules: [
      { required: true, message: '请输入VC账号编码' },
      { pattern: /^[A-Z0-9_]{3,20}$/, message: 'VC账号编码格式不正确，只能包含大写字母、数字和下划线，长度3-20位' },
    ],
  },
  {
    label: 'VC账号名称',
    field: 'accountName',
    required: true,
    component: 'Input',
    componentProps: {
      placeholder: '请输入VC账号名称',
      maxlength: 200,
    },
  },
  {
    label: '账号类型',
    field: 'accountType',
    component: 'Select',
    componentProps: {
      options: VC_ACCOUNT_TYPE_OPTIONS,
      placeholder: '请选择账号类型',
    },
  },
  {
    label: '公司名称',
    field: 'companyName',
    component: 'Input',
    componentProps: {
      placeholder: '请输入公司名称',
      maxlength: 300,
    },
  },
  {
    label: '联系人',
    field: 'contactPerson',
    component: 'Input',
    componentProps: {
      placeholder: '请输入联系人',
      maxlength: 100,
    },
  },
  {
    label: '联系电话',
    field: 'contactPhone',
    component: 'Input',
    componentProps: {
      placeholder: '请输入联系电话',
      maxlength: 50,
    },
    rules: [
      { pattern: /^1[3-9]\d{9}$|^0\d{2,3}-?\d{7,8}$|^$/, message: '联系电话格式不正确' },
    ],
  },
  {
    label: '联系邮箱',
    field: 'contactEmail',
    component: 'Input',
    componentProps: {
      placeholder: '请输入联系邮箱',
      maxlength: 100,
    },
    rules: [
      { type: 'email', message: '联系邮箱格式不正确' },
    ],
  },
  {
    label: '经营范围',
    field: 'businessScope',
    component: 'InputTextArea',
    componentProps: {
      placeholder: '请输入经营范围',
      rows: 3,
    },
  },
  {
    label: '所属区域',
    field: 'regionCode',
    component: 'Input',
    componentProps: {
      placeholder: '请输入区域编码',
      maxlength: 50,
    },
  },
  {
    label: '区域名称',
    field: 'regionName',
    component: 'Input',
    componentProps: {
      placeholder: '请输入区域名称',
      maxlength: 100,
    },
  },
  {
    label: '负责人',
    field: 'managerUserId',
    required: true,
    component: 'ApiSelect',
    componentProps: {
      api: getUserSimpleList,
      labelField: 'nickname',
      valueField: 'id',
      placeholder: '请选择负责人',
    },
  },
  {
    label: '备用负责人',
    field: 'backupManagerUserId',
    component: 'ApiSelect',
    componentProps: {
      api: getUserSimpleList,
      labelField: 'nickname',
      valueField: 'id',
      placeholder: '请选择备用负责人',
    },
  },
  {
    label: '优先级',
    field: 'priorityLevel',
    component: 'RadioButtonGroup',
    defaultValue: 3,
    componentProps: {
      options: [
        { label: '高', value: 1 },
        { label: '中', value: 2 },
        { label: '低', value: 3 },
      ],
    },
  },
  {
    label: '状态',
    field: 'status',
    required: true,
    component: 'RadioButtonGroup',
    defaultValue: 1,
    componentProps: {
      options: VC_ACCOUNT_STATUS_OPTIONS,
    },
  },
  {
    label: '备注',
    field: 'remark',
    component: 'InputTextArea',
    componentProps: {
      placeholder: '请输入备注',
      rows: 3,
      maxlength: 500,
    },
  },
]
